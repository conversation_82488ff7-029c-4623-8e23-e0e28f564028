-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- <PERSON>reate custom types
CREATE TYPE review_dimension AS ENUM (
  'PROJECT_FUNDAMENTALS',
  'TEAM_GOVERNANCE',
  'TRANSPARENCY_DOCUMENTATION',
  'TECHNOLOGY_EXECUTION',
  'COMMUNITY_COMMUNICATION',
  'TOKEN_UTILITY_TOKENOMICS'
);

-- Create users table
CREATE TABLE public.users (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  is_kyc_verified BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create projects table
CREATE TABLE public.projects (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  image_url TEXT,
  website_url TEXT,
  deck_url TEXT,
  whitepaper_url TEXT,
  social_urls JSONB,
  challenge_intro TEXT NOT NULL,
  is_approved_for_voting BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create reviews table
CREATE TABLE public.reviews (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  project_id UUID NOT NULL REFERENCES public.projects(id) ON DELETE CASCADE,
  overall_sentiment BOOLEAN NOT NULL,
  overall_comments TEXT,
  overall_comments_relevant BOOLEAN DEFAULT TRUE,
  overall_relevance_score INTEGER DEFAULT 50,
  overall_validation_reason TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, project_id)
);

-- Create review_responses table
CREATE TABLE public.review_responses (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  review_id UUID NOT NULL REFERENCES public.reviews(id) ON DELETE CASCADE,
  dimension review_dimension NOT NULL,
  question_index INTEGER NOT NULL,
  vote BOOLEAN NOT NULL,
  feedback TEXT,
  feedback_relevant BOOLEAN DEFAULT TRUE,
  relevance_score INTEGER DEFAULT 50,
  validation_reason TEXT,
  ai_confidence INTEGER DEFAULT 0,
  UNIQUE(review_id, dimension, question_index)
);

-- Create ai_analyses table
CREATE TABLE public.ai_analyses (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  project_id UUID UNIQUE NOT NULL REFERENCES public.projects(id) ON DELETE CASCADE,
  project_fundamentals_score INTEGER DEFAULT 0,
  team_governance_score INTEGER DEFAULT 0,
  transparency_doc_score INTEGER DEFAULT 0,
  technology_execution_score INTEGER DEFAULT 0,
  community_communication_score INTEGER DEFAULT 0,
  token_utility_tokenomics_score INTEGER DEFAULT 0,
  overall_score INTEGER DEFAULT 0,
  analysis TEXT,
  reasoning TEXT,
  strengths TEXT[],
  weaknesses TEXT[],
  recommendations TEXT[],
  confidence INTEGER DEFAULT 0,
  analysis_version TEXT DEFAULT '1.0',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better query performance
CREATE INDEX idx_users_email ON public.users(email);
CREATE INDEX idx_reviews_user_id ON public.reviews(user_id);
CREATE INDEX idx_reviews_project_id ON public.reviews(project_id);
CREATE INDEX idx_reviews_created_at ON public.reviews(created_at);
CREATE INDEX idx_review_responses_review_id ON public.review_responses(review_id);
CREATE INDEX idx_review_responses_dimension ON public.review_responses(dimension);
CREATE INDEX idx_projects_created_at ON public.projects(created_at);
CREATE INDEX idx_projects_is_approved ON public.projects(is_approved_for_voting);
CREATE INDEX idx_ai_analyses_project_id ON public.ai_analyses(project_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for ai_analyses updated_at
CREATE TRIGGER update_ai_analyses_updated_at 
  BEFORE UPDATE ON public.ai_analyses 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.review_responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ai_analyses ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Users can read all users but only update their own
CREATE POLICY "Users can view all users" ON public.users FOR SELECT USING (true);
CREATE POLICY "Users can update own profile" ON public.users FOR UPDATE USING (auth.uid()::text = id::text);

-- Projects are readable by all, but only admins can create/update
CREATE POLICY "Anyone can view projects" ON public.projects FOR SELECT USING (true);
CREATE POLICY "Only authenticated users can create projects" ON public.projects FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Reviews can be read by all, but users can only create/update their own
CREATE POLICY "Anyone can view reviews" ON public.reviews FOR SELECT USING (true);
CREATE POLICY "Users can create their own reviews" ON public.reviews FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);
CREATE POLICY "Users can update their own reviews" ON public.reviews FOR UPDATE USING (auth.uid()::text = user_id::text);

-- Review responses follow the same pattern as reviews
CREATE POLICY "Anyone can view review responses" ON public.review_responses FOR SELECT USING (true);
CREATE POLICY "Users can create review responses for their reviews" ON public.review_responses 
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.reviews 
      WHERE id = review_id AND user_id::text = auth.uid()::text
    )
  );
CREATE POLICY "Users can update review responses for their reviews" ON public.review_responses 
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.reviews 
      WHERE id = review_id AND user_id::text = auth.uid()::text
    )
  );

-- AI analyses are readable by all, but only system can create/update
CREATE POLICY "Anyone can view AI analyses" ON public.ai_analyses FOR SELECT USING (true);
CREATE POLICY "Only service role can manage AI analyses" ON public.ai_analyses FOR ALL USING (auth.role() = 'service_role');

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL TABLES IN SCHEMA public TO postgres, service_role;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT INSERT, UPDATE, DELETE ON public.users TO authenticated;
GRANT INSERT ON public.projects TO authenticated;
GRANT INSERT, UPDATE, DELETE ON public.reviews TO authenticated;
GRANT INSERT, UPDATE, DELETE ON public.review_responses TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO postgres, service_role;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;