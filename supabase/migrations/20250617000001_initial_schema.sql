-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- <PERSON>reate custom types
CREATE TYPE review_dimension AS ENUM (
  'PROJECT_FUNDAMENTALS',
  'TEAM_GOVERNANCE', 
  'TRANSPARENCY_DOCUMENTATION',
  'TECHNOLOGY_EXECUTION',
  'COMMUNITY_COMMUNICATION',
  'TOKEN_UTILITY_TOKENOMICS'
);

-- Create users table
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  is_kyc_verified BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  -- Link to Supabase auth
  auth_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Create projects table
CREATE TABLE projects (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  image_url TEXT,
  website_url TEXT,
  deck_url TEXT,
  whitepaper_url TEXT,
  social_urls JSONB,
  challenge_intro TEXT NOT NULL,
  is_approved_for_voting BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create reviews table
CREATE TABLE reviews (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  overall_sentiment BOOLEAN NOT NULL,
  overall_comments TEXT,
  overall_comments_relevant BOOLEAN DEFAULT TRUE,
  overall_relevance_score INTEGER DEFAULT 50,
  overall_validation_reason TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, project_id)
);

-- Create review_responses table
CREATE TABLE review_responses (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  review_id UUID NOT NULL REFERENCES reviews(id) ON DELETE CASCADE,
  dimension review_dimension NOT NULL,
  question_index INTEGER NOT NULL,
  vote BOOLEAN NOT NULL,
  feedback TEXT,
  feedback_relevant BOOLEAN DEFAULT TRUE,
  relevance_score INTEGER DEFAULT 50,
  validation_reason TEXT,
  ai_confidence INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(review_id, dimension, question_index)
);

-- Create ai_analyses table
CREATE TABLE ai_analyses (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  project_id UUID NOT NULL UNIQUE REFERENCES projects(id) ON DELETE CASCADE,
  project_fundamentals_score INTEGER DEFAULT 0,
  team_governance_score INTEGER DEFAULT 0,
  transparency_doc_score INTEGER DEFAULT 0,
  technology_execution_score INTEGER DEFAULT 0,
  community_communication_score INTEGER DEFAULT 0,
  token_utility_tokenomics_score INTEGER DEFAULT 0,
  overall_score INTEGER DEFAULT 0,
  analysis TEXT,
  reasoning TEXT,
  strengths TEXT[],
  weaknesses TEXT[],
  recommendations TEXT[],
  confidence INTEGER DEFAULT 0,
  analysis_version TEXT DEFAULT '1.0',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX users_auth_user_id_idx ON users(auth_user_id);
CREATE INDEX users_email_idx ON users(email);
CREATE INDEX users_kyc_verified_idx ON users(is_kyc_verified);

CREATE INDEX projects_approved_idx ON projects(is_approved_for_voting);
CREATE INDEX projects_created_at_idx ON projects(created_at DESC);

CREATE INDEX reviews_user_id_idx ON reviews(user_id);
CREATE INDEX reviews_project_id_idx ON reviews(project_id);
CREATE INDEX reviews_sentiment_idx ON reviews(overall_sentiment);
CREATE INDEX reviews_created_at_idx ON reviews(created_at DESC);

CREATE INDEX review_responses_review_id_idx ON review_responses(review_id);
CREATE INDEX review_responses_dimension_idx ON review_responses(dimension);
CREATE INDEX review_responses_vote_idx ON review_responses(vote);

CREATE INDEX ai_analyses_project_id_idx ON ai_analyses(project_id);

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_reviews_updated_at BEFORE UPDATE ON reviews
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ai_analyses_updated_at BEFORE UPDATE ON ai_analyses
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE review_responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_analyses ENABLE ROW LEVEL SECURITY;

-- Helper functions for RLS policies
CREATE OR REPLACE FUNCTION auth.user_email()
RETURNS TEXT
LANGUAGE sql STABLE
AS $$
  SELECT COALESCE(
    auth.jwt() ->> 'email',
    (auth.jwt() -> 'user_metadata' ->> 'email'),
    (auth.jwt() -> 'raw_user_meta_data' ->> 'email')
  )::TEXT;
$$;

CREATE OR REPLACE FUNCTION is_admin()
RETURNS BOOLEAN
LANGUAGE sql STABLE
AS $$
  SELECT auth.user_email() LIKE '%@ibc.media';
$$;

CREATE OR REPLACE FUNCTION is_kyc_verified()
RETURNS BOOLEAN
LANGUAGE sql STABLE
AS $$
  SELECT EXISTS (
    SELECT 1 FROM users 
    WHERE auth_user_id = auth.uid() 
    AND is_kyc_verified = TRUE
  );
$$;

-- Users policies
CREATE POLICY "Users can view their own profile" 
  ON users FOR SELECT 
  USING (auth.uid() = auth_user_id);

CREATE POLICY "Users can update their own profile" 
  ON users FOR UPDATE 
  USING (auth.uid() = auth_user_id);

CREATE POLICY "Users can insert their own profile" 
  ON users FOR INSERT 
  WITH CHECK (auth.uid() = auth_user_id);

-- Projects policies
CREATE POLICY "Anyone can view approved projects" 
  ON projects FOR SELECT 
  USING (is_approved_for_voting = TRUE OR is_admin());

CREATE POLICY "Admins can manage projects" 
  ON projects FOR ALL 
  USING (is_admin());

-- Reviews policies
CREATE POLICY "Anyone can view all reviews" 
  ON reviews FOR SELECT 
  TO authenticated 
  USING (TRUE);

CREATE POLICY "KYC users can create reviews" 
  ON reviews FOR INSERT 
  TO authenticated 
  WITH CHECK (
    is_kyc_verified() AND
    EXISTS (
      SELECT 1 FROM users 
      WHERE auth_user_id = auth.uid() 
      AND id = user_id
    )
  );

CREATE POLICY "Users can update their own reviews" 
  ON reviews FOR UPDATE 
  TO authenticated 
  USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE auth_user_id = auth.uid() 
      AND id = user_id
    )
  );

-- Review responses policies
CREATE POLICY "Anyone can view all review responses" 
  ON review_responses FOR SELECT 
  TO authenticated 
  USING (TRUE);

CREATE POLICY "Users can create responses for their reviews" 
  ON review_responses FOR INSERT 
  TO authenticated 
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM reviews r
      JOIN users u ON r.user_id = u.id
      WHERE r.id = review_id 
      AND u.auth_user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update their own review responses" 
  ON review_responses FOR UPDATE 
  TO authenticated 
  USING (
    EXISTS (
      SELECT 1 FROM reviews r
      JOIN users u ON r.user_id = u.id
      WHERE r.id = review_id 
      AND u.auth_user_id = auth.uid()
    )
  );

-- AI analyses policies
CREATE POLICY "Anyone can view AI analyses" 
  ON ai_analyses FOR SELECT 
  USING (TRUE);

CREATE POLICY "Only admins can manage AI analyses" 
  ON ai_analyses FOR ALL 
  USING (is_admin());

-- Create storage bucket for project assets
INSERT INTO storage.buckets (id, name, public) 
VALUES ('project-assets', 'project-assets', true);

-- Storage policies
CREATE POLICY "Anyone can view project assets"
  ON storage.objects FOR SELECT
  USING (bucket_id = 'project-assets');

CREATE POLICY "Admins can upload project assets"
  ON storage.objects FOR INSERT
  WITH CHECK (
    bucket_id = 'project-assets' AND is_admin()
  );

CREATE POLICY "Admins can update project assets"
  ON storage.objects FOR UPDATE
  USING (bucket_id = 'project-assets' AND is_admin());

CREATE POLICY "Admins can delete project assets"
  ON storage.objects FOR DELETE
  USING (bucket_id = 'project-assets' AND is_admin());