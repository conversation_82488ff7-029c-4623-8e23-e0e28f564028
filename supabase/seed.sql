-- Insert test users
INSERT INTO auth.users (
  instance_id,
  id,
  aud,
  role,
  email,
  encrypted_password,
  email_confirmed_at,
  recovery_sent_at,
  last_sign_in_at,
  raw_app_meta_data,
  raw_user_meta_data,
  created_at,
  updated_at,
  confirmation_token,
  email_change,
  email_change_token_new,
  recovery_token
) VALUES (
  '00000000-0000-0000-0000-000000000000',
  'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11',
  'authenticated',
  'authenticated',
  '<EMAIL>',
  '$2a$10$yQP/rqN5U0Dz1G5QhTVJ8eXJZz9vK3vJzQ4kX7Tz1CJz1bY4zN1Ka',
  NOW(),
  NOW(),
  NOW(),
  '{"provider": "email", "providers": ["email"]}',
  '{"email": "<EMAIL>", "name": "Admin User"}',
  NOW(),
  NOW(),
  '',
  '',
  '',
  ''
), (
  '00000000-0000-0000-0000-000000000000',
  'b1ffdc00-ad1c-5ef9-cc7e-7cc0ce491b22',
  'authenticated',
  'authenticated',
  '<EMAIL>',
  '$2a$10$yQP/rqN5U0Dz1G5QhTVJ8eXJZz9vK3vJzQ4kX7Tz1CJz1bY4zN1Ka',
  NOW(),
  NOW(),
  NOW(),
  '{"provider": "email", "providers": ["email"]}',
  '{"email": "<EMAIL>", "name": "Alice Johnson"}',
  NOW(),
  NOW(),
  '',
  '',
  '',
  ''
), (
  '00000000-0000-0000-0000-000000000000',
  'c2ggdd11-be2d-6fga-dd8f-8dd1df592c33',
  'authenticated',
  'authenticated',
  '<EMAIL>',
  '$2a$10$yQP/rqN5U0Dz1G5QhTVJ8eXJZz9vK3vJzQ4kX7Tz1CJz1bY4zN1Ka',
  NOW(),
  NOW(),
  NOW(),
  '{"provider": "email", "providers": ["email"]}',
  '{"email": "<EMAIL>", "name": "Bob Smith"}',
  NOW(),
  NOW(),
  '',
  '',
  '',
  ''
);

-- Insert user profiles
INSERT INTO users (id, email, name, is_kyc_verified, auth_user_id) VALUES
('550e8400-e29b-41d4-a716-446655440000', '<EMAIL>', 'Admin User', true, 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11'),
('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'Alice Johnson', true, 'b1ffdc00-ad1c-5ef9-cc7e-7cc0ce491b22'),
('550e8400-e29b-41d4-a716-446655440002', '<EMAIL>', 'Bob Smith', false, 'c2ggdd11-be2d-6fga-dd8f-8dd1df592c33');

-- Insert sample projects
INSERT INTO projects (id, title, description, image_url, website_url, deck_url, whitepaper_url, social_urls, challenge_intro, is_approved_for_voting) VALUES
('6ba7b810-9dad-11d1-80b4-00c04fd430c8', 
 'Cosmos Hub', 
 'The Cosmos Hub is the first blockchain in the Cosmos Network, designed to facilitate interoperability between blockchains through the Inter-Blockchain Communication (IBC) protocol.',
 'https://example.com/cosmos-hub.png',
 'https://hub.cosmos.network',
 'https://example.com/cosmos-deck.pdf',
 'https://example.com/cosmos-whitepaper.pdf',
 '{"twitter": "https://twitter.com/cosmoshub", "discord": "https://discord.gg/cosmosnetwork", "telegram": "https://t.me/cosmosproject"}',
 'As the foundational blockchain of the Cosmos ecosystem, how effectively does Cosmos Hub serve as the central hub for inter-blockchain communication and security?',
 true),

('6ba7b811-9dad-11d1-80b4-00c04fd430c8',
 'Osmosis',
 'Osmosis is an advanced automated market maker (AMM) protocol built using the Cosmos SDK that allows developers to design, build, and deploy their own customized AMMs.',
 'https://example.com/osmosis.png',
 'https://osmosis.zone',
 'https://example.com/osmosis-deck.pdf',
 'https://example.com/osmosis-whitepaper.pdf',
 '{"twitter": "https://twitter.com/OsmosisZone", "discord": "https://discord.gg/osmosis", "telegram": "https://t.me/osmosis_chat"}',
 'How well does Osmosis serve as the premier decentralized exchange and liquidity hub within the Cosmos ecosystem?',
 true),

('6ba7b812-9dad-11d1-80b4-00c04fd430c8',
 'Juno Network',
 'Juno is a global, open source, permission-less network for decentralized interoperable applications. The network serves as a decentralized, censorship resistant way for developers to efficiently and securely launch smart contracts using proven frameworks and compile them in various languages.',
 'https://example.com/juno.png',
 'https://junonetwork.io',
 'https://example.com/juno-deck.pdf',
 'https://example.com/juno-whitepaper.pdf',
 '{"twitter": "https://twitter.com/JunoNetwork", "discord": "https://discord.gg/juno", "telegram": "https://t.me/JunoNetwork"}',
 'How effectively does Juno Network provide a robust and developer-friendly platform for smart contract deployment in the Cosmos ecosystem?',
 true),

('6ba7b813-9dad-11d1-80b4-00c04fd430c8',
 'Secret Network',
 'Secret Network is a blockchain with data privacy by default, allowing you to build and use applications that are both permissionless and privacy-preserving. This unique functionality protects users, secures applications, and unlocks hundreds of never-before-possible use cases for Web3.',
 'https://example.com/secret.png',
 'https://scrt.network',
 'https://example.com/secret-deck.pdf',
 'https://example.com/secret-whitepaper.pdf',
 '{"twitter": "https://twitter.com/SecretNetwork", "discord": "https://discord.gg/secret", "telegram": "https://t.me/SCRTcommunity"}',
 'How well does Secret Network balance privacy, functionality, and decentralization in its privacy-preserving blockchain architecture?',
 true),

('6ba7b814-9dad-11d1-80b4-00c04fd430c8',
 'Akash Network',
 'Akash Network is the world''s first decentralized cloud computing marketplace, enabling any person to buy and sell computing resources securely and efficiently. Purpose-built for public utility.',
 'https://example.com/akash.png',
 'https://akash.network',
 'https://example.com/akash-deck.pdf',
 'https://example.com/akash-whitepaper.pdf',
 '{"twitter": "https://twitter.com/akashnet_", "discord": "https://discord.gg/akash", "telegram": "https://t.me/AkashNW"}',
 'How effectively does Akash Network provide a viable decentralized alternative to traditional cloud computing services?',
 true);

-- Insert sample reviews
INSERT INTO reviews (id, user_id, project_id, overall_sentiment, overall_comments, overall_comments_relevant, overall_relevance_score) VALUES
('7ca7c820-aead-22e2-91c5-11d14fe541d9', '550e8400-e29b-41d4-a716-************', '6ba7b810-9dad-11d1-80b4-00c04fd430c8', true, 'Cosmos Hub has established itself as the cornerstone of interoperability in blockchain. The IBC protocol is revolutionary and the validator set is robust. However, there are concerns about governance efficiency and the need for more active development.', true, 85),
('7ca7c821-aead-22e2-91c5-11d14fe541d9', '550e8400-e29b-41d4-a716-************', '6ba7b811-9dad-11d1-80b4-00c04fd430c8', true, 'Osmosis has created an innovative AMM design with concentrated liquidity and superfluid staking. The user experience is excellent and trading fees are competitive. The main concern is the complexity for new users and potential smart contract risks.', true, 90),
('7ca7c822-aead-22e2-91c5-11d14fe541d9', '550e8400-e29b-41d4-a716-************', '6ba7b812-9dad-11d1-80b4-00c04fd430c8', false, 'While Juno has good developer tools and CosmWasm integration, the network has faced significant challenges with governance controversies and validator centralization. The recent proposals and their handling raise concerns about true decentralization.', true, 75);

-- Insert sample review responses
INSERT INTO review_responses (id, review_id, dimension, question_index, vote, feedback, feedback_relevant, relevance_score) VALUES
-- Cosmos Hub review responses
('8da8d930-bfbe-33f3-a2d6-22e25gf652ea', '7ca7c820-aead-22e2-91c5-11d14fe541d9', 'PROJECT_FUNDAMENTALS', 0, true, 'Strong foundation with proven IBC protocol and clear utility as the hub for Cosmos ecosystem', true, 90),
('8da8d931-bfbe-33f3-a2d6-22e25gf652ea', '7ca7c820-aead-22e2-91c5-11d14fe541d9', 'PROJECT_FUNDAMENTALS', 1, true, 'Well-defined use case as the interoperability hub', true, 85),
('8da8d932-bfbe-33f3-a2d6-22e25gf652ea', '7ca7c820-aead-22e2-91c5-11d14fe541d9', 'TEAM_GOVERNANCE', 0, false, 'Governance processes can be slow and sometimes contentious', true, 70),
('8da8d933-bfbe-33f3-a2d6-22e25gf652ea', '7ca7c820-aead-22e2-91c5-11d14fe541d9', 'TEAM_GOVERNANCE', 1, true, 'Core development team is experienced and competent', true, 80),
('8da8d934-bfbe-33f3-a2d6-22e25gf652ea', '7ca7c820-aead-22e2-91c5-11d14fe541d9', 'TRANSPARENCY_DOCUMENTATION', 0, true, 'Good documentation and transparent development process', true, 85),
('8da8d935-bfbe-33f3-a2d6-22e25gf652ea', '7ca7c820-aead-22e2-91c5-11d14fe541d9', 'TECHNOLOGY_EXECUTION', 0, true, 'IBC protocol works well and continues to improve', true, 90),
('8da8d936-bfbe-33f3-a2d6-22e25gf652ea', '7ca7c820-aead-22e2-91c5-11d14fe541d9', 'COMMUNITY_COMMUNICATION', 0, true, 'Active community and regular updates', true, 80),
('8da8d937-bfbe-33f3-a2d6-22e25gf652ea', '7ca7c820-aead-22e2-91c5-11d14fe541d9', 'TOKEN_UTILITY_TOKENOMICS', 0, true, 'ATOM has clear utility for staking and governance', true, 85),

-- Osmosis review responses  
('8da8d940-bfbe-33f3-a2d6-22e25gf652ea', '7ca7c821-aead-22e2-91c5-11d14fe541d9', 'PROJECT_FUNDAMENTALS', 0, true, 'Innovative AMM design with concentrated liquidity and superfluid staking', true, 95),
('8da8d941-bfbe-33f3-a2d6-22e25gf652ea', '7ca7c821-aead-22e2-91c5-11d14fe541d9', 'PROJECT_FUNDAMENTALS', 1, true, 'Clear value proposition as the main DEX for Cosmos', true, 90),
('8da8d942-bfbe-33f3-a2d6-22e25gf652ea', '7ca7c821-aead-22e2-91c5-11d14fe541d9', 'TEAM_GOVERNANCE', 0, true, 'Strong development team with good governance practices', true, 85),
('8da8d943-bfbe-33f3-a2d6-22e25gf652ea', '7ca7c821-aead-22e2-91c5-11d14fe541d9', 'TRANSPARENCY_DOCUMENTATION', 0, true, 'Excellent documentation and transparent roadmap', true, 90),
('8da8d944-bfbe-33f3-a2d6-22e25gf652ea', '7ca7c821-aead-22e2-91c5-11d14fe541d9', 'TECHNOLOGY_EXECUTION', 0, true, 'Complex but well-executed technology stack', true, 90),
('8da8d945-bfbe-33f3-a2d6-22e25gf652ea', '7ca7c821-aead-22e2-91c5-11d14fe541d9', 'COMMUNITY_COMMUNICATION', 0, true, 'Very active community and great user experience', true, 95),
('8da8d946-bfbe-33f3-a2d6-22e25gf652ea', '7ca7c821-aead-22e2-91c5-11d14fe541d9', 'TOKEN_UTILITY_TOKENOMICS', 0, true, 'OSMO token has multiple utilities and good tokenomics', true, 90),

-- Juno review responses
('8da8d950-bfbe-33f3-a2d6-22e25gf652ea', '7ca7c822-aead-22e2-91c5-11d14fe541d9', 'PROJECT_FUNDAMENTALS', 0, true, 'Good smart contract platform with CosmWasm integration', true, 80),
('8da8d951-bfbe-33f3-a2d6-22e25gf652ea', '7ca7c822-aead-22e2-91c5-11d14fe541d9', 'TEAM_GOVERNANCE', 0, false, 'Recent governance controversies and validator centralization issues', true, 60),
('8da8d952-bfbe-33f3-a2d6-22e25gf652ea', '7ca7c822-aead-22e2-91c5-11d14fe541d9', 'TRANSPARENCY_DOCUMENTATION', 0, false, 'Some controversial decisions lacked transparency', true, 65),
('8da8d953-bfbe-33f3-a2d6-22e25gf652ea', '7ca7c822-aead-22e2-91c5-11d14fe541d9', 'TECHNOLOGY_EXECUTION', 0, true, 'Solid technical execution with CosmWasm', true, 85),
('8da8d954-bfbe-33f3-a2d6-22e25gf652ea', '7ca7c822-aead-22e2-91c5-11d14fe541d9', 'COMMUNITY_COMMUNICATION', 0, false, 'Community trust has been damaged by recent events', true, 50),
('8da8d955-bfbe-33f3-a2d6-22e25gf652ea', '7ca7c822-aead-22e2-91c5-11d14fe541d9', 'TOKEN_UTILITY_TOKENOMICS', 0, true, 'JUNO token mechanics are sound', true, 75);

-- Insert sample AI analyses
INSERT INTO ai_analyses (
  id, 
  project_id, 
  project_fundamentals_score,
  team_governance_score,
  transparency_doc_score,
  technology_execution_score,
  community_communication_score,
  token_utility_tokenomics_score,
  overall_score,
  analysis,
  reasoning,
  strengths,
  weaknesses,
  recommendations,
  confidence
) VALUES
('9ea9ea40-cfcf-44g4-b3e7-33f36hg763fb', 
 '6ba7b810-9dad-11d1-80b4-00c04fd430c8',
 88, 75, 85, 90, 80, 85, 84,
 'Cosmos Hub represents a foundational piece of blockchain infrastructure with strong technical merit. The IBC protocol is a significant innovation that enables true interoperability between blockchains. However, governance challenges and the need for more active development pose ongoing concerns.',
 'The analysis is based on the project''s pioneering role in blockchain interoperability, strong technical foundations, but balanced against governance inefficiencies and development pace concerns raised by the community.',
 ARRAY['First mover in blockchain interoperability', 'Proven IBC protocol', 'Strong validator network', 'Clear utility as ecosystem hub'],
 ARRAY['Governance can be slow and contentious', 'Development pace concerns', 'Competition from other ecosystems', 'Complexity for new users'],
 ARRAY['Improve governance efficiency', 'Accelerate development roadmap', 'Better onboarding for new users', 'Enhanced developer tools'],
 85),

('9ea9ea41-cfcf-44g4-b3e7-33f36hg763fb',
 '6ba7b811-9dad-11d1-80b4-00c04fd430c8', 
 92, 85, 90, 90, 95, 90, 90,
 'Osmosis has established itself as the premier DEX in the Cosmos ecosystem with innovative features like concentrated liquidity and superfluid staking. The project demonstrates strong execution, community engagement, and continuous innovation.',
 'High scores reflect the project''s technical innovation, strong community adoption, excellent user experience, and clear value proposition within the Cosmos ecosystem.',
 ARRAY['Innovative AMM design', 'Excellent user experience', 'Strong community', 'Continuous development', 'Multiple revenue streams'],
 ARRAY['Complexity for new users', 'Smart contract risks', 'Dependency on Cosmos ecosystem', 'Competition from other DEXs'],
 ARRAY['Simplify user interface', 'Enhanced security audits', 'Cross-chain expansion', 'Educational content'],
 90),

('9ea9ea42-cfcf-44g4-b3e7-33f36hg763fb',
 '6ba7b812-9dad-11d1-80b4-00c04fd430c8',
 80, 60, 65, 85, 50, 75, 69,
 'Juno Network shows strong technical capabilities with CosmWasm integration but has faced significant governance and community challenges. Recent controversies have impacted trust and raised questions about decentralization.',
 'Mixed scores reflect strong technical foundations balanced against significant governance issues, community trust problems, and centralization concerns that have emerged over time.',
 ARRAY['Good developer tools', 'CosmWasm integration', 'Technical competence', 'Smart contract capabilities'],
 ARRAY['Governance controversies', 'Validator centralization', 'Community trust issues', 'Transparency concerns'],
 ARRAY['Address governance issues', 'Improve validator decentralization', 'Rebuild community trust', 'Enhance transparency'],
 75);