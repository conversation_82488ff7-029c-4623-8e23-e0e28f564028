# Dependencies
node_modules
.pnpm-store

# Turbo
.turbo

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production
.env.staging

# Build outputs
dist
build
.next
.react-router

# Cache directories
.cache
.parcel-cache
.vite

# OS files
.DS_Store
Thumbs.db

# IDE files
.vscode/settings.json
.idea
*.swp
*.swo
*~

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.test
.env.development
.env.production
.env.staging
.env
.env.local

# Supabase
.branches
.temp
supabase/.branches
supabase/.temp

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Local development
.vercel
