{"name": "IBC-CIE", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo dev", "build": "turbo build", "check-types": "turbo check-types", "dev:native": "turbo -F native dev", "dev:web": "turbo -F web dev", "dev:server": "turbo -F server dev", "supabase:start": "supabase start", "supabase:stop": "supabase stop", "supabase:reset": "supabase db reset", "supabase:status": "supabase status", "supabase:types": "supabase gen types --lang=typescript --project-id=\"$PROJECT_REF\" --schema=public > apps/server/src/types/supabase.ts", "supabase:types:local": "supabase gen types --lang=typescript --local > apps/server/src/types/supabase.ts", "supabase:migrate": "supabase db push", "supabase:seed": "supabase seed", "supabase:studio": "supabase studio", "db:reset": "pnpm supabase:reset", "db:seed": "pnpm supabase:seed", "db:studio": "pnpm supabase:studio", "db:types": "pnpm supabase:types:local", "setup:local": "supabase start && pnpm supabase:types:local", "validate:env": "node scripts/validate-env.js", "health:check": "./scripts/health-check.sh", "reset:db": "./scripts/reset-database.sh"}, "dependencies": {"@supabase/supabase-js": "^2.50.0", "dotenv": "^16.5.0"}, "devDependencies": {"supabase": "^1.220.3", "turbo": "^2.5.4"}, "packageManager": "pnpm@9.0.0"}