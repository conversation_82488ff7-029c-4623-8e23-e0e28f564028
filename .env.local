# Local Development Environment
# Copy this file to .env.local for local development

# Supabase Local Development
SUPABASE_URL=https://vzvgaycuxzddjqmcywbg.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZ6dmdheWN1eHpkZGpxbWN5d2JnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDE2MTMzNTksImV4cCI6MjA1NzE4OTM1OX0.WaIY6PcA9uX4JFI0Ru--I4yq_mJvhHUmiZoFL1aDm30
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZ6dmdheWN1eHpkZGpxbWN5d2JnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTYxMzM1OSwiZXhwIjoyMDU3MTg5MzU5fQ.PEDo-rcfLM569_p9vy5wZCdZDPaJz2cYg3cJd8tHEBQ

# OpenAI API Key (replace with your actual key)
OPENAI_API_KEY=sk-your-openai-key-here

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
VITE_SERVER_URL=http://localhost:3001
CORS_ORIGIN=http://localhost:5174

# Development Settings
NODE_ENV=development
AUTH_SECRET=your-local-auth-secret-for-development-only

# Local Database Connection
DATABASE_URL=postgresql://postgres:postgres@127.0.0.1:54322/postgres