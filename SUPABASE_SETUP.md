# Supabase Setup Guide for IBC-CIE

This guide provides comprehensive instructions for setting up and managing the Supabase environment for the IBC-CIE project.

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- Docker and Docker Compose
- pnpm (preferred) or npm
- Supabase CLI

### Automatic Setup

Run the automated setup script:

```bash
# Make script executable (if not already)
chmod +x scripts/setup-local.sh

# Run the setup
./scripts/setup-local.sh
```

This script will:
- Check and install dependencies
- Set up environment files
- Start Supabase services
- Run migrations and seed data
- Generate TypeScript types
- Validate the setup

### Manual Setup

If you prefer manual setup or need to troubleshoot:

1. **Install Supabase CLI**:
   ```bash
   # macOS
   brew install supabase/tap/supabase
   
   # npm (cross-platform)
   npm install -g supabase
   ```

2. **Install dependencies**:
   ```bash
   pnpm install
   ```

3. **Set up environment**:
   ```bash
   cp .env.local.example .env.local
   # Edit .env.local with your OpenAI API key
   ```

4. **Start Supabase**:
   ```bash
   pnpm supabase:start
   ```

5. **Generate types**:
   ```bash
   pnpm db:types
   ```

## 📋 Available Commands

### Root Project Commands

```bash
# Supabase Management
pnpm supabase:start        # Start all Supabase services
pnpm supabase:stop         # Stop all Supabase services  
pnpm supabase:reset        # Reset database with migrations
pnpm supabase:status       # Show status of all services
pnpm supabase:studio       # Open Supabase Studio UI

# Database Operations
pnpm db:reset              # Reset database (alias for supabase:reset)
pnpm db:seed               # Run seed data only
pnpm db:studio             # Open database studio (alias)
pnpm db:types              # Generate TypeScript types from schema

# Development
pnpm dev                   # Start all development servers
pnpm setup:local           # Complete local environment setup
```

### Server-Specific Commands

```bash
cd apps/server
pnpm db:types              # Generate types locally
pnpm db:seed               # Run seed script
```

## 🗄️ Database Schema

The database includes the following main tables:

### Core Tables

- **`users`** - User accounts and KYC status
- **`projects`** - Blockchain projects submitted for review
- **`reviews`** - User reviews of projects
- **`review_responses`** - Individual dimension responses within reviews
- **`ai_analyses`** - AI-generated project analyses

### Key Features

- **Row Level Security (RLS)** enabled on all tables
- **Real-time subscriptions** for live updates
- **Enum types** for standardized review dimensions
- **JSON columns** for flexible metadata storage
- **Comprehensive indexes** for query performance

## 🔧 Configuration

### Environment Variables

#### Development (.env.local)
```env
SUPABASE_URL=http://127.0.0.1:54321
SUPABASE_ANON_KEY=your-local-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-local-service-key
OPENAI_API_KEY=your-openai-key
```

#### Production (.env.production)
```env
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-production-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-production-service-key
```

### Supabase Configuration

The `supabase/config.toml` file configures:
- Local development ports
- Authentication settings
- Storage limits
- Real-time features
- Edge functions

## 🔐 Security & Authentication

### Row Level Security Policies

- **Users**: Can view all, update own profile only
- **Projects**: Public read, authenticated create
- **Reviews**: Public read, users manage own reviews
- **AI Analyses**: Public read, service role manages

### Authentication Flow

1. Users authenticate via Supabase Auth
2. JWT tokens contain user claims
3. RLS policies enforce data access rules
4. Service role bypasses RLS for admin operations

## 📊 Development Workflow

### Daily Development

1. **Start services**:
   ```bash
   pnpm supabase:start
   ```

2. **Start development servers**:
   ```bash
   pnpm dev
   ```

3. **Access services**:
   - Web app: http://localhost:5174
   - Server API: http://localhost:3000
   - Supabase Studio: http://localhost:54323

### Database Changes

1. **Create migration**:
   ```bash
   supabase migration new your_migration_name
   ```

2. **Edit migration file** in `supabase/migrations/`

3. **Apply migration**:
   ```bash
   pnpm supabase:reset
   ```

4. **Generate new types**:
   ```bash
   pnpm db:types
   ```

### Schema Updates

When modifying the database schema:

1. Create a new migration file
2. Update seed data if needed
3. Regenerate TypeScript types
4. Update application code to use new types
5. Test thoroughly before committing

## 🚨 Troubleshooting

### Common Issues

#### Supabase won't start
```bash
# Check Docker status
docker ps

# Restart Docker and try again
pnpm supabase:stop
pnpm supabase:start
```

#### Missing types
```bash
# Regenerate types
pnpm db:types

# Or use local generation
pnpm supabase:types:local
```

#### Connection errors
```bash
# Check Supabase status
pnpm supabase:status

# Verify environment variables
cat .env.local
```

#### Database reset issues
```bash
# Force reset with script
./scripts/reset-database.sh --force

# Or manual reset
pnpm supabase:stop
docker system prune -f
pnpm supabase:start
```

### Getting Help

1. **Check logs**:
   ```bash
   supabase logs
   ```

2. **Verify configuration**:
   ```bash
   supabase status
   ```

3. **Reset everything**:
   ```bash
   ./scripts/reset-database.sh --force
   ```

## 🌐 Production Deployment

### Supabase Cloud Setup

1. **Create project** at [supabase.com](https://supabase.com)

2. **Get connection details** from project settings

3. **Set production environment variables**:
   ```env
   SUPABASE_URL=https://your-project.supabase.co
   SUPABASE_ANON_KEY=your-production-anon-key
   SUPABASE_SERVICE_ROLE_KEY=your-production-service-key
   ```

4. **Run migrations**:
   ```bash
   supabase db push --linked
   ```

5. **Generate production types**:
   ```bash
   pnpm supabase:types
   ```

### CI/CD Integration

Add to your deployment pipeline:

```yaml
# Example GitHub Actions step
- name: Deploy to Supabase
  run: |
    supabase link --project-ref ${{ secrets.SUPABASE_PROJECT_REF }}
    supabase db push
```

## 📚 Additional Resources

- [Supabase Documentation](https://supabase.com/docs)
- [Supabase CLI Reference](https://supabase.com/docs/reference/cli)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Row Level Security Guide](https://supabase.com/docs/guides/auth/row-level-security)

## 🤝 Contributing

When contributing database changes:

1. Create a descriptive migration name
2. Include both up and down migrations when possible
3. Update seed data to reflect schema changes
4. Test migrations on a fresh database
5. Update this documentation if needed

## 📋 Migration Checklist

Before deploying database changes:

- [ ] Migration tested locally
- [ ] Seed data updated
- [ ] TypeScript types regenerated
- [ ] Application code updated
- [ ] RLS policies reviewed
- [ ] Performance impact assessed
- [ ] Backup strategy confirmed