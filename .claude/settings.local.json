{"permissions": {"allow": ["Bash(bun add:*)", "Bash(bun db:generate:*)", "Bash(bun db:push:*)", "Bash(bun run:*)", "Bash(bun:*)", "<PERSON><PERSON>(pkill:*)", "Bash(PORT=5173 bun run dev)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(timeout 30 bun run dev)", "Bash(rm:*)", "Bash(npm install:*)", "Bash(pnpm install:*)", "Bash(pnpm dev:*)", "Bash(pnpm db:studio:*)", "Bash(git add:*)", "Bash(gh repo create:*)", "Bash(pnpm check-types:*)", "Bash(npx tsc:*)", "Bash(pnpm build:*)", "Bash(pnpm db:push:*)", "Bash(pnpm run:*)", "<PERSON><PERSON>(tsx:*)", "Bash(npx tsx:*)", "Bash(ls:*)", "Bash(pnpm add:*)", "mcp__exa__web_search_exa", "Bash(find:*)", "<PERSON><PERSON>(chmod:*)", "Bash(node:*)", "Bash(npx supabase:*)"], "deny": []}}