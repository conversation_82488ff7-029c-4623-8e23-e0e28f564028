# Supabase Configuration
SUPABASE_URL=your_supabase_project_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Frontend Environment Variables (apps/web)
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Backend Environment Variables (apps/server)
OPENROUTER_API_KEY=your_openrouter_api_key

# Database (Legacy - can be removed after migration)
DATABASE_URL=postgresql://username:password@localhost:5432/database_name

# Development URLs
VITE_SERVER_URL=http://localhost:3000
CORS_ORIGIN=http://localhost:5173

# Optional: Email service (for auth emails)
RESEND_API_KEY=your_resend_api_key

# Optional: KYC verification service
JUMIO_API_TOKEN=your_jumio_api_token
JUMIO_API_SECRET=your_jumio_api_secret