# Production Environment Configuration
# Use this template for production deployment

# Production Supabase
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your-production-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-production-service-role-key

# Project Reference for type generation
PROJECT_REF=your-project-ref

# OpenAI API
OPENAI_API_KEY=your-production-openai-key

# Production URLs
NEXT_PUBLIC_APP_URL=https://your-domain.com
VITE_SERVER_URL=https://your-domain.com
CORS_ORIGIN=https://your-frontend-domain.com

# Production Settings
NODE_ENV=production
AUTH_SECRET=your-secure-production-auth-secret

# Production Database (optional, for direct access)
DATABASE_URL=postgresql://postgres.your-project-id:<EMAIL>:5432/postgres

# Optional: Analytics & Monitoring
# SENTRY_DSN=your-sentry-dsn
# ANALYTICS_ID=your-analytics-id

# Optional: CDN Configuration
# CDN_URL=https://your-cdn-domain.com

# Optional: Rate Limiting
# RATE_LIMIT_MAX=100
# RATE_LIMIT_WINDOW_MS=60000