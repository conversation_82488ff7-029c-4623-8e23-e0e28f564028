{"name": "web", "private": true, "type": "module", "scripts": {"build": "react-router build", "dev": "react-router dev", "start": "react-router-serve ./build/server/index.js", "typecheck": "react-router typegen && tsc"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "@react-router/fs-routes": "^7.6.1", "@react-router/node": "^7.6.1", "@react-router/serve": "^7.6.1", "@supabase/supabase-js": "^2.48.0", "@tanstack/react-form": "^1.12.0", "@tanstack/react-query": "^5.80.5", "@trpc/client": "^11.0.0", "@trpc/react-query": "^11.4.1", "@trpc/server": "^11.0.0", "@trpc/tanstack-react-query": "^11.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "isbot": "^5.1.28", "lucide-react": "^0.511.0", "next-themes": "^0.4.6", "radix-ui": "^1.4.2", "react": "19.0.0", "react-dom": "19.0.0", "react-router": "^7.6.1", "recharts": "^2.15.3", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "tw-animate-css": "^1.3.2", "zod": "^3.25.42"}, "devDependencies": {"@react-router/dev": "^7.6.1", "@tailwindcss/vite": "^4.1.8", "@tanstack/react-query-devtools": "^5.80.5", "@types/node": "^20", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react-swc": "^3.5.0", "react-router-devtools": "^1.1.0", "tailwindcss": "^4.1.8", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-tsconfig-paths": "^5.1.4"}}