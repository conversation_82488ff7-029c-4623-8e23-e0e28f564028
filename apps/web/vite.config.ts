import { reactRouter } from "@react-router/dev/vite";
import tailwindcss from "@tailwindcss/vite";
import { defineConfig } from "vite";
import tsconfigPaths from "vite-tsconfig-paths";
import react from "@vitejs/plugin-react-swc";

export default defineConfig({
  plugins: [
    // Order matters: React plugin should come before React Router
    react({
      // Ensure SWC handles JSX properly
      jsxImportSource: "react",
    }),
    tailwindcss(),
    reactRouter(),
    tsconfigPaths(),
  ],
  resolve: {
    alias: {},
    dedupe: ["react", "react-dom"],
  },
  ssr: {
    external: ["react", "react-dom"],
  },
  esbuild: {
    // Ensure esbuild handles JSX properly
    jsx: "automatic",
    jsxImportSource: "react",
  },
});
