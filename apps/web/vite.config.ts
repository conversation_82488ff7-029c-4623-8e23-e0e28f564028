import { reactRouter } from "@react-router/dev/vite";
import tailwindcss from "@tailwindcss/vite";
import { defineConfig } from "vite";
import tsconfigPaths from "vite-tsconfig-paths";

export default defineConfig({
  plugins: [
    // React Router plugin handles React internally, no need for separate React plugin
    tailwindcss(),
    reactRouter(),
    tsconfigPaths(),
  ],
  resolve: {
    alias: {},
    dedupe: ["react", "react-dom"],
  },
  ssr: {
    external: ["react", "react-dom"],
  },
});
