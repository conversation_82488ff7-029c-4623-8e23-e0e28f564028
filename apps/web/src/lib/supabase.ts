import { createClient } from '@supabase/supabase-js'
import type { Database } from '../types/database'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL!
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY!

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey)

// Database types
export type Tables<T extends keyof Database['public']['Tables']> = 
  Database['public']['Tables'][T]['Row']

export type Enums<T extends keyof Database['public']['Enums']> = 
  Database['public']['Enums'][T]

export type User = Tables<'users'>
export type Project = Tables<'projects'>
export type Review = Tables<'reviews'>
export type ReviewResponse = Tables<'review_responses'>
export type AIAnalysis = Tables<'ai_analyses'>

// Authentication helpers
export const signUp = async (email: string, password: string, name: string) => {
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: {
        name,
      },
    },
  })

  if (error) throw error

  // Create user profile
  if (data.user) {
    const { error: profileError } = await supabase
      .from('users')
      .insert({
        auth_user_id: data.user.id,
        email: data.user.email!,
        name,
      })
    
    if (profileError) throw profileError
  }

  return data
}

export const signIn = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  })

  if (error) throw error
  return data
}

export const signOut = async () => {
  const { error } = await supabase.auth.signOut()
  if (error) throw error
}

export const getCurrentUser = async () => {
  const { data: { user } } = await supabase.auth.getUser()
  return user
}

export const getCurrentUserProfile = async () => {
  const user = await getCurrentUser()
  if (!user) return null

  const { data, error } = await supabase
    .from('users')
    .select('*')
    .eq('auth_user_id', user.id)
    .single()

  if (error) throw error
  return data
}

// Real-time helpers
export const subscribeToReviews = (
  projectId: string,
  callback: (payload: any) => void
) => {
  const subscription = supabase
    .channel(`reviews:${projectId}`)
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'reviews',
        filter: `project_id=eq.${projectId}`,
      },
      callback
    )
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'review_responses',
      },
      callback
    )
    .subscribe()

  return () => subscription.unsubscribe()
}

export const subscribeToProject = (
  projectId: string,
  callback: (payload: any) => void
) => {
  const subscription = supabase
    .channel(`project:${projectId}`)
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'projects',
        filter: `id=eq.${projectId}`,
      },
      callback
    )
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'ai_analyses',
        filter: `project_id=eq.${projectId}`,
      },
      callback
    )
    .subscribe()

  return () => subscription.unsubscribe()
}

// Error handling
export class SupabaseError extends Error {
  constructor(message: string, public code?: string) {
    super(message)
    this.name = 'SupabaseError'
  }
}

export const handleSupabaseError = (error: any): never => {
  if (error.code === 'PGRST301') {
    throw new SupabaseError('Duplicate entry', error.code)
  }
  if (error.code === 'PGRST116') {
    throw new SupabaseError('Not found', error.code)
  }
  throw new SupabaseError(error.message || 'Unknown error', error.code)
}