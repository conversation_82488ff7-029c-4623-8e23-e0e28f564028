import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "./ui/card";
import { Label } from "./ui/label";
import { Textarea } from "./ui/textarea";
import { ThumbsVote } from "./thumbs-vote";
import type { DimensionInfo } from "../lib/review-constants";

interface DimensionReviewProps {
  dimension: DimensionInfo;
  vote?: boolean;
  onVoteChange: (vote: boolean) => void;
  feedback: string;
  onFeedbackChange: (feedback: string) => void;
  disabled?: boolean;
}

export function DimensionReview({ 
  dimension, 
  vote,
  onVoteChange,
  feedback,
  onFeedbackChange,
  disabled = false 
}: DimensionReviewProps) {

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg font-semibold">
              {dimension.title}
            </CardTitle>
            {dimension.subtitle && (
              <p className="text-sm text-muted-foreground">{dimension.subtitle}</p>
            )}
          </div>
          <ThumbsVote
            value={vote}
            onChange={onVoteChange}
            disabled={disabled}
          />
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Questions Section */}
          <div className="space-y-4">
            <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">
              Key Questions to Consider
            </h4>
            <div className="space-y-3">
              {dimension.questions.map((question, index) => (
                <div key={index} className="p-3 bg-muted/50 rounded-lg">
                  <Label className="text-sm leading-relaxed">{question}</Label>
                </div>
              ))}
            </div>
          </div>

          {/* Feedback Section */}
          <div className="space-y-4">
            <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">
              Written Feedback
            </h4>
            <Textarea
              placeholder="Share your detailed thoughts on this dimension..."
              value={feedback}
              onChange={(e) => onFeedbackChange(e.target.value)}
              disabled={disabled}
              className="min-h-[200px] resize-none"
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}