import { <PERSON><PERSON>ircle, Alert<PERSON>riangle, XCircle, Info } from "lucide-react";
import { Badge } from "./ui/badge";

interface ValidationBadgeProps {
  isRelevant: boolean;
  relevanceScore: number;
  confidence: number;
  reason?: string;
  size?: "sm" | "md";
}

export function ValidationBadge({
  isRelevant,
  relevanceScore,
  confidence,
  reason,
  size = "sm"
}: ValidationBadgeProps) {
  const getVariantAndIcon = () => {
    if (confidence < 20) {
      return {
        variant: "secondary" as const,
        icon: Info,
        text: "Unvalidated",
        description: "AI validation unavailable"
      };
    }
    
    if (isRelevant && relevanceScore >= 8) {
      return {
        variant: "default" as const,
        icon: CheckCircle,
        text: "Relevant",
        description: `High relevance (${relevanceScore}/10)`
      };
    }
    
    if (isRelevant && relevanceScore >= 5) {
      return {
        variant: "secondary" as const,
        icon: AlertTriangle,
        text: "Moderate",
        description: `Moderate relevance (${relevanceScore}/10)`
      };
    }
    
    return {
      variant: "destructive" as const,
      icon: XCircle,
      text: "Low Quality",
      description: `Low relevance (${relevanceScore}/10)`
    };
  };

  const { variant, icon: Icon, text, description } = getVariantAndIcon();
  
  return (
    <div className="group relative">
      <Badge 
        variant={variant} 
        className={`flex items-center gap-1 ${
          size === "sm" ? "text-xs px-2 py-1" : "text-sm px-3 py-1"
        }`}
      >
        <Icon className={size === "sm" ? "h-3 w-3" : "h-4 w-4"} />
        {text}
      </Badge>
      
      {/* Tooltip */}
      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-black text-white text-xs rounded-md opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-10">
        <div className="font-medium">{description}</div>
        {reason && (
          <div className="text-gray-300 mt-1 max-w-48 whitespace-normal">
            {reason}
          </div>
        )}
        <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-black"></div>
      </div>
    </div>
  );
}