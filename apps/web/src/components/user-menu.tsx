import { useState } from 'react'
import { useAuth } from './auth-provider'
import { Button } from './ui/button'
import { Badge } from './ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from './ui/dropdown-menu'
import { AuthModal } from './auth-form'

export function UserMenu() {
  const { user, userProfile, loading, signOut } = useAuth()
  const [showAuthModal, setShowAuthModal] = useState(false)
  const [authMode, setAuthMode] = useState<'signin' | 'signup'>('signin')

  if (loading) {
    return (
      <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse" />
    )
  }

  if (!user || !userProfile) {
    return (
      <>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            onClick={() => {
              setAuthMode('signin')
              setShowAuthModal(true)
            }}
          >
            Sign In
          </Button>
          <Button
            onClick={() => {
              setAuthMode('signup')
              setShowAuthModal(true)
            }}
          >
            Sign Up
          </Button>
        </div>
        <AuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
          initialMode={authMode}
        />
      </>
    )
  }

  const isAdmin = userProfile.email.endsWith('@ibc.media')

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="flex items-center gap-2">
          <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
            {userProfile.name.charAt(0).toUpperCase()}
          </div>
          <span className="hidden md:inline">{userProfile.name}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <div className="px-2 py-1.5">
          <p className="text-sm font-medium">{userProfile.name}</p>
          <p className="text-xs text-muted-foreground">{userProfile.email}</p>
          <div className="flex items-center gap-2 mt-2">
            {userProfile.is_kyc_verified ? (
              <Badge variant="default" className="text-xs">
                KYC Verified
              </Badge>
            ) : (
              <Badge variant="secondary" className="text-xs">
                KYC Pending
              </Badge>
            )}
            {isAdmin && (
              <Badge variant="destructive" className="text-xs">
                Admin
              </Badge>
            )}
          </div>
        </div>
        <DropdownMenuSeparator />
        
        <DropdownMenuItem>
          Profile Settings
        </DropdownMenuItem>
        
        {!userProfile.is_kyc_verified && (
          <DropdownMenuItem>
            Complete KYC Verification
          </DropdownMenuItem>
        )}
        
        {isAdmin && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuItem>
              Admin Dashboard
            </DropdownMenuItem>
            <DropdownMenuItem>
              Manage Projects
            </DropdownMenuItem>
          </>
        )}
        
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onClick={signOut}
          className="text-red-600 focus:text-red-600"
        >
          Sign Out
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

// Simplified user status display
export function UserStatus() {
  const { user, userProfile, loading } = useAuth()

  if (loading) {
    return <div className="text-sm text-muted-foreground">Loading...</div>
  }

  if (!user || !userProfile) {
    return <div className="text-sm text-muted-foreground">Not signed in</div>
  }

  return (
    <div className="flex items-center gap-2">
      <span className="text-sm text-muted-foreground">
        Signed in as {userProfile.name}
      </span>
      {userProfile.is_kyc_verified ? (
        <Badge variant="default" className="text-xs">
          KYC Verified
        </Badge>
      ) : (
        <Badge variant="secondary" className="text-xs">
          KYC Pending
        </Badge>
      )}
    </div>
  )
}

// Protection wrapper for KYC-required actions
export function KYCRequired({ 
  children, 
  fallback 
}: { 
  children: React.ReactNode
  fallback?: React.ReactNode 
}) {
  const { userProfile } = useAuth()

  if (!userProfile?.is_kyc_verified) {
    return (
      fallback || (
        <div className="text-center py-8 px-4">
          <div className="text-muted-foreground mb-4">
            KYC verification is required to submit reviews and participate in the evaluation process.
          </div>
          <Badge variant="secondary">
            KYC Verification Required
          </Badge>
        </div>
      )
    )
  }

  return <>{children}</>
}

// Protection wrapper for admin-only actions
export function AdminRequired({ 
  children, 
  fallback 
}: { 
  children: React.ReactNode
  fallback?: React.ReactNode 
}) {
  const { userProfile } = useAuth()
  const isAdmin = userProfile?.email.endsWith('@ibc.media')

  if (!isAdmin) {
    return (
      fallback || (
        <div className="text-center py-8 px-4">
          <div className="text-muted-foreground mb-4">
            Admin access is required to view this content.
          </div>
          <Badge variant="destructive">
            Admin Access Required
          </Badge>
        </div>
      )
    )
  }

  return <>{children}</>
}