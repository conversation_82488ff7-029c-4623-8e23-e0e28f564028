import { useState } from "react";
import { useNavigate } from "react-router";
import { Button } from "./ui/button";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "./ui/card";
import { Label } from "./ui/label";
import { Textarea } from "./ui/textarea";
import { DimensionReview } from "./dimension-review";
import { ThumbsVote } from "./thumbs-vote";
import { toast } from "sonner";
import { REVIEW_DIMENSIONS, type ReviewDimension } from "../lib/review-constants";
import { trpc } from "./trpc-provider";

interface DimensionVotes {
  [key: string]: boolean | undefined;
}

interface DimensionFeedback {
  [key: string]: string;
}

interface Project {
  id: string;
  title: string;
  description: string;
  imageUrl: string | null;
  websiteUrl: string | null;
  deckUrl: string | null;
  whitepaperUrl: string | null;
  socialUrls: any;
  challengeIntro: string;
}

interface ReviewFormProps {
  project: Project;
}

export function ReviewForm({ project }: ReviewFormProps) {
  const navigate = useNavigate();
  const isDebugMode = import.meta.env.VITE_DEBUG_MODE === "true";
  const [overallSentiment, setOverallSentiment] = useState<boolean | undefined>();
  const [overallComments, setOverallComments] = useState("");
  const [dimensionVotes, setDimensionVotes] = useState<DimensionVotes>(() => {
    const initial: DimensionVotes = {};
    REVIEW_DIMENSIONS.forEach(dimension => {
      initial[dimension.key] = undefined;
    });
    return initial;
  });

  const [dimensionFeedback, setDimensionFeedback] = useState<DimensionFeedback>(() => {
    const initial: DimensionFeedback = {};
    REVIEW_DIMENSIONS.forEach(dimension => {
      initial[dimension.key] = "";
    });
    return initial;
  });

  const createReviewMutation = trpc.reviews.create.useMutation({
    onSuccess: () => {
      toast.success("Review submitted successfully! AI validation has been applied to your feedback.");
      navigate(`/projects/${project.id}`);
    },
    onError: (error) => {
      toast.error(`Failed to submit review: ${error.message}`);
    },
  });

  const handleDimensionVoteChange = (
    dimensionKey: ReviewDimension,
    vote: boolean
  ) => {
    setDimensionVotes(prev => ({
      ...prev,
      [dimensionKey]: vote,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (overallSentiment === undefined) {
      alert("Please provide an overall sentiment");
      return;
    }

    // Create responses for submission - one per dimension with aggregated feedback
    const responses = REVIEW_DIMENSIONS.flatMap(dimension => {
      const vote = dimensionVotes[dimension.key];
      if (vote === undefined) return []; // Skip dimensions without votes
      
      // Create one response per question in the dimension with the same vote
      return dimension.questions.map((_, questionIndex) => ({
        dimension: dimension.key,
        questionIndex,
        vote,
        feedback: dimensionFeedback[dimension.key] || "",
      }));
    });

    // Check if at least some dimensions have votes
    if (responses.length === 0) {
      alert("Please vote on at least one dimension before submitting");
      return;
    }

    createReviewMutation.mutate({
      projectId: project.id,
      overallSentiment,
      overallComments,
      responses,
    });
  };

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Debug Mode Indicator */}
      {isDebugMode && (
        <Card className="border-orange-200 bg-orange-50">
          <CardContent className="py-4">
            <div className="flex items-center gap-2 text-orange-700">
              <span className="text-lg">🐛</span>
              <span className="font-medium">Debug Mode Active</span>
              <span className="text-sm">- Multiple reviews per user allowed</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Project Header */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="w-full sm:w-32 h-32 sm:h-20 bg-muted rounded-lg flex items-center justify-center overflow-hidden">
              {project.imageUrl ? (
                <img 
                  src={project.imageUrl} 
                  alt={project.title}
                  className="w-full h-full object-cover"
                />
              ) : (
                <span className="text-2xl">IMG</span>
              )}
            </div>
            <div className="flex-1">
              <CardTitle className="text-2xl mb-2">{project.title}</CardTitle>
              <p className="text-muted-foreground">{project.description}</p>
              
              {/* Project Links */}
              <div className="flex flex-wrap gap-2 mt-3">
                {project.websiteUrl && (
                  <Button variant="outline" size="sm" asChild>
                    <a href={project.websiteUrl} target="_blank" rel="noopener noreferrer">
                      Website
                    </a>
                  </Button>
                )}
                {project.deckUrl && (
                  <Button variant="outline" size="sm" asChild>
                    <a href={project.deckUrl} target="_blank" rel="noopener noreferrer">
                      Deck
                    </a>
                  </Button>
                )}
                {project.whitepaperUrl && (
                  <Button variant="outline" size="sm" asChild>
                    <a href={project.whitepaperUrl} target="_blank" rel="noopener noreferrer">
                      Whitepaper
                    </a>
                  </Button>
                )}
                {project.socialUrls && (
                  <Button variant="outline" size="sm" asChild>
                    <a href="#" target="_blank" rel="noopener noreferrer">
                      Socials
                    </a>
                  </Button>
                )}
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <p className="text-sm leading-relaxed">{project.challengeIntro}</p>
          </div>
        </CardContent>
      </Card>

      {/* Review Form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Dimension Reviews */}
        {REVIEW_DIMENSIONS.map((dimension, index) => (
          <DimensionReview
            key={dimension.key}
            dimension={dimension}
            vote={dimensionVotes[dimension.key]}
            onVoteChange={(vote) => handleDimensionVoteChange(dimension.key, vote)}
            feedback={dimensionFeedback[dimension.key]}
            onFeedbackChange={(feedback) => 
              setDimensionFeedback(prev => ({ ...prev, [dimension.key]: feedback }))
            }
            disabled={createReviewMutation.isPending}
          />
        ))}

        {/* Overall Sentiment */}
        <Card>
          <CardHeader>
            <CardTitle>Overall Sentiment</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="text-base">Want to see this listed paragraph?</Label>
              <ThumbsVote
                value={overallSentiment}
                onChange={setOverallSentiment}
                disabled={createReviewMutation.isPending}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="overall-feedback">Overall Feedback</Label>
              <Textarea
                id="overall-feedback"
                placeholder="Share your overall thoughts about this project..."
                value={overallComments}
                onChange={(e) => setOverallComments(e.target.value)}
                disabled={createReviewMutation.isPending}
                className="min-h-[100px]"
              />
            </div>
          </CardContent>
        </Card>

        {/* Submit Button */}
        <div className="text-center space-y-3">
          <Button 
            type="submit" 
            size="lg" 
            disabled={createReviewMutation.isPending}
            className="w-full sm:w-auto"
          >
            {createReviewMutation.isPending ? "Submitting & Validating..." : "Submit Review"}
          </Button>
          <p className="text-xs text-muted-foreground">
            🤖 AI will validate your feedback for relevance and quality
          </p>
        </div>
      </form>

      {/* CTA Box */}
      <Card className="bg-muted/50">
        <CardContent className="p-6 text-center">
          <p className="text-sm text-muted-foreground">CTA box TBD</p>
        </CardContent>
      </Card>
    </div>
  );
}