import { createContext, useContext, useEffect, useState } from 'react'
import type { User as SupabaseUser } from '@supabase/supabase-js'
import { supabase, signIn, signUp, signOut, getCurrentUserProfile } from '../lib/supabase'
import type { User } from '../lib/supabase'

interface AuthContextType {
  user: SupabaseUser | null
  userProfile: User | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<void>
  signUp: (email: string, password: string, name: string) => Promise<void>
  signOut: () => Promise<void>
  refreshProfile: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | null>(null)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<SupabaseUser | null>(null)
  const [userProfile, setUserProfile] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession()
        setUser(session?.user ?? null)
        
        if (session?.user) {
          const profile = await getCurrentUserProfile()
          setUserProfile(profile)
        }
      } catch (error) {
        console.error('Error getting initial session:', error)
      } finally {
        setLoading(false)
      }
    }

    getInitialSession()

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (_event, session) => {
      setUser(session?.user ?? null)
      
      if (session?.user) {
        try {
          const profile = await getCurrentUserProfile()
          setUserProfile(profile)
        } catch (error) {
          console.error('Error fetching user profile:', error)
          setUserProfile(null)
        }
      } else {
        setUserProfile(null)
      }
      
      setLoading(false)
    })

    return () => subscription.unsubscribe()
  }, [])

  const handleSignIn = async (email: string, password: string) => {
    try {
      await signIn(email, password)
    } catch (error) {
      throw error
    }
  }

  const handleSignUp = async (email: string, password: string, name: string) => {
    try {
      await signUp(email, password, name)
    } catch (error) {
      throw error
    }
  }

  const handleSignOut = async () => {
    try {
      await signOut()
    } catch (error) {
      throw error
    }
  }

  const refreshProfile = async () => {
    if (user) {
      try {
        const profile = await getCurrentUserProfile()
        setUserProfile(profile)
      } catch (error) {
        console.error('Error refreshing profile:', error)
      }
    }
  }

  return (
    <AuthContext.Provider
      value={{
        user,
        userProfile,
        loading,
        signIn: handleSignIn,
        signUp: handleSignUp,
        signOut: handleSignOut,
        refreshProfile,
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// Hook for protected routes
export const useRequireAuth = () => {
  const { user, userProfile, loading } = useAuth()
  
  if (loading) {
    return { loading: true, user: null, userProfile: null }
  }
  
  if (!user || !userProfile) {
    throw new Error('Authentication required')
  }
  
  return { loading: false, user, userProfile }
}

// Hook for KYC verification requirement
export const useRequireKYC = () => {
  const { user, userProfile, loading } = useAuth()
  
  if (loading) {
    return { loading: true, user: null, userProfile: null, isKycVerified: false }
  }
  
  if (!user || !userProfile) {
    throw new Error('Authentication required')
  }
  
  if (!userProfile.is_kyc_verified) {
    throw new Error('KYC verification required')
  }
  
  return { 
    loading: false, 
    user, 
    userProfile, 
    isKycVerified: userProfile.is_kyc_verified 
  }
}

// Hook for admin access
export const useRequireAdmin = () => {
  const { user, userProfile, loading } = useAuth()
  
  if (loading) {
    return { loading: true, user: null, userProfile: null, isAdmin: false }
  }
  
  if (!user || !userProfile) {
    throw new Error('Authentication required')
  }
  
  const isAdmin = userProfile.email.endsWith('@ibc.media')
  
  if (!isAdmin) {
    throw new Error('Admin access required')
  }
  
  return { 
    loading: false, 
    user, 
    userProfile, 
    isAdmin 
  }
}