import { useState } from 'react'
import { useAuth } from './auth-provider'
import { Button } from './ui/button'
import { Input } from './ui/input'
import { Label } from './ui/label'
import { Card } from './ui/card'

interface AuthFormProps {
  mode?: 'signin' | 'signup'
  onModeChange?: (mode: 'signin' | 'signup') => void
  onSuccess?: () => void
}

export function AuthForm({ 
  mode = 'signin', 
  onModeChange, 
  onSuccess 
}: AuthFormProps) {
  const { signIn, signUp } = useAuth()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    name: '',
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    try {
      if (mode === 'signup') {
        if (!formData.name.trim()) {
          throw new Error('Name is required')
        }
        await signUp(formData.email, formData.password, formData.name)
      } else {
        await signIn(formData.email, formData.password)
      }
      
      onSuccess?.()
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: keyof typeof formData) => (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setFormData(prev => ({ ...prev, [field]: e.target.value }))
  }

  return (
    <Card className="w-full max-w-md mx-auto p-6">
      <div className="space-y-4">
        <div className="text-center">
          <h2 className="text-2xl font-semibold">
            {mode === 'signup' ? 'Create Account' : 'Sign In'}
          </h2>
          <p className="text-muted-foreground mt-2">
            {mode === 'signup' 
              ? 'Join the IBC Community Intelligence Engine' 
              : 'Welcome back to IBC-CIE'
            }
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {mode === 'signup' && (
            <div className="space-y-2">
              <Label htmlFor="name">Full Name</Label>
              <Input
                id="name"
                type="text"
                value={formData.name}
                onChange={handleInputChange('name')}
                placeholder="Enter your full name"
                required
              />
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={handleInputChange('email')}
              placeholder="Enter your email"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <Input
              id="password"
              type="password"
              value={formData.password}
              onChange={handleInputChange('password')}
              placeholder="Enter your password"
              required
              minLength={6}
            />
            {mode === 'signup' && (
              <p className="text-sm text-muted-foreground">
                Password must be at least 6 characters long
              </p>
            )}
          </div>

          {error && (
            <div className="text-sm text-red-500 bg-red-50 p-3 rounded-md">
              {error}
            </div>
          )}

          <Button
            type="submit"
            className="w-full"
            disabled={loading}
          >
            {loading 
              ? (mode === 'signup' ? 'Creating Account...' : 'Signing In...') 
              : (mode === 'signup' ? 'Create Account' : 'Sign In')
            }
          </Button>
        </form>

        {onModeChange && (
          <div className="text-center text-sm">
            {mode === 'signup' ? (
              <span>
                Already have an account?{' '}
                <button
                  type="button"
                  onClick={() => onModeChange('signin')}
                  className="text-blue-600 hover:underline font-medium"
                >
                  Sign in
                </button>
              </span>
            ) : (
              <span>
                Don't have an account?{' '}
                <button
                  type="button"
                  onClick={() => onModeChange('signup')}
                  className="text-blue-600 hover:underline font-medium"
                >
                  Create one
                </button>
              </span>
            )}
          </div>
        )}

        {mode === 'signup' && (
          <div className="text-xs text-muted-foreground text-center space-y-2">
            <p>
              By creating an account, you agree to our Terms of Service and Privacy Policy.
            </p>
            <p className="font-medium">
              Note: KYC verification will be required to submit project reviews.
            </p>
          </div>
        )}
      </div>
    </Card>
  )
}

// Modal variant
export function AuthModal({ 
  isOpen, 
  onClose, 
  initialMode = 'signin' 
}: {
  isOpen: boolean
  onClose: () => void
  initialMode?: 'signin' | 'signup'
}) {
  const [mode, setMode] = useState(initialMode)

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-auto">
        <div className="flex justify-between items-center p-4 border-b">
          <h3 className="text-lg font-semibold">
            {mode === 'signup' ? 'Create Account' : 'Sign In'}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            ×
          </button>
        </div>
        <div className="p-4">
          <AuthForm
            mode={mode}
            onModeChange={setMode}
            onSuccess={onClose}
          />
        </div>
      </div>
    </div>
  )
}