import type { Route } from "./+types/projects.$id";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router";
import { ArrowLeft, ExternalLink, Globe, FileText, Users } from "lucide-react";
import { trpc } from "@/components/trpc-provider";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { SpiderChart } from "@/components/spider-chart";
import { Loader } from "@/components/loader";
import { ValidationBadge } from "@/components/validation-badge";

export function meta({ params }: Route.MetaArgs) {
  return [
    { title: `Project Details - ATTN CIE` },
    { name: "description", content: `View project details and community reviews` }
  ];
}

export default function ProjectDetail() {
  const { id } = useParams();
  const projectQuery = trpc.projects.getById.useQuery({ id: id! });
  const reviewsQuery = trpc.reviews.getByProject.useQuery({ projectId: id! });

  if (projectQuery.isLoading) {
    return (
      <div className="container mx-auto max-w-6xl px-4 py-8">
        <div className="flex justify-center py-16">
          <Loader />
        </div>
      </div>
    );
  }

  if (projectQuery.error || !projectQuery.data) {
    return (
      <div className="container mx-auto max-w-6xl px-4 py-8">
        <div className="text-center py-16">
          <h1 className="text-2xl font-bold mb-4">Project Not Found</h1>
          <p className="text-muted-foreground mb-6">
            The project you're looking for doesn't exist or has been removed.
          </p>
          <Button asChild>
            <Link to="/">Return Home</Link>
          </Button>
        </div>
      </div>
    );
  }

  const project = projectQuery.data;

  return (
    <div className="container mx-auto max-w-6xl px-4 py-8">
      {/* Back Button */}
      <Button variant="ghost" asChild className="mb-6">
        <Link to="/">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Projects
        </Link>
      </Button>

      {/* Project Header */}
      <Card className="mb-8">
        <CardHeader>
          <div className="flex flex-col lg:flex-row gap-6">
            <div className="w-full lg:w-64 h-48 lg:h-32 bg-muted rounded-lg flex items-center justify-center overflow-hidden">
              {project.imageUrl ? (
                <img 
                  src={project.imageUrl} 
                  alt={project.title}
                  className="w-full h-full object-cover"
                />
              ) : (
                <span className="text-4xl">IMG</span>
              )}
            </div>
            
            <div className="flex-1 space-y-4">
              <div>
                <CardTitle className="text-3xl mb-2">{project.title}</CardTitle>
                <p className="text-lg text-muted-foreground">{project.description}</p>
              </div>
              
              {/* Project Links */}
              <div className="flex flex-wrap gap-2">
                {project.websiteUrl && (
                  <Button variant="outline" asChild>
                    <a href={project.websiteUrl} target="_blank" rel="noopener noreferrer">
                      <Globe className="h-4 w-4 mr-2" />
                      Website
                    </a>
                  </Button>
                )}
                {project.deckUrl && (
                  <Button variant="outline" asChild>
                    <a href={project.deckUrl} target="_blank" rel="noopener noreferrer">
                      <FileText className="h-4 w-4 mr-2" />
                      Deck
                    </a>
                  </Button>
                )}
                {project.whitepaperUrl && (
                  <Button variant="outline" asChild>
                    <a href={project.whitepaperUrl} target="_blank" rel="noopener noreferrer">
                      <FileText className="h-4 w-4 mr-2" />
                      Whitepaper
                    </a>
                  </Button>
                )}
                <Button variant="outline" asChild>
                  <a href="#" target="_blank" rel="noopener noreferrer">
                    <Users className="h-4 w-4 mr-2" />
                    Socials
                  </a>
                </Button>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3">
                {project.isApprovedForVoting && (
                  <Button asChild>
                    <Link to={`/projects/${project.id}/review`}>
                      Submit Review
                    </Link>
                  </Button>
                )}
                <Button variant="outline" asChild>
                  <a href="#reviews">View Reviews ({project.totalReviews})</a>
                </Button>
              </div>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="font-medium mb-2">Challenge Overview</h3>
            <p className="text-sm leading-relaxed">{project.challengeIntro}</p>
          </div>
        </CardContent>
      </Card>


      {/* Reviews Section */}
      <div className="grid lg:grid-cols-2 gap-8">
        {/* Spider Chart */}
        <SpiderChart
          scores={project.dimensionScores}
          title={`Community Review Scores (${project.totalReviews} reviews)`}
        />

        {/* Review Summary */}
        <Card>
          <CardHeader>
            <CardTitle>Review Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-center">
                <div className="bg-muted/50 rounded-lg p-4">
                  <div className="text-2xl font-bold">{project.totalReviews}</div>
                  <div className="text-sm text-muted-foreground">Total Reviews</div>
                </div>
                <div className="bg-muted/50 rounded-lg p-4">
                  <div className="text-2xl font-bold">
                    {project.dimensionScores.length > 0 
                      ? Math.round(project.dimensionScores.reduce((sum, d) => sum + d.score, 0) / project.dimensionScores.length)
                      : 0}%
                  </div>
                  <div className="text-sm text-muted-foreground">Average Score</div>
                </div>
              </div>

              {/* Dimension Breakdown */}
              <div className="space-y-2">
                <h4 className="font-medium">Dimension Scores</h4>
                {project.dimensionScores.map((dimension) => (
                  <div key={dimension.dimension} className="flex justify-between items-center text-sm">
                    <span className="text-muted-foreground">{dimension.title}</span>
                    <span className="font-medium">{dimension.score}%</span>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Individual Reviews */}
      <div id="reviews" className="mt-12">
        <h2 className="text-2xl font-semibold mb-6">Individual Reviews</h2>
        
        {reviewsQuery.isLoading && (
          <div className="flex justify-center py-8">
            <Loader />
          </div>
        )}

        {reviewsQuery.data && reviewsQuery.data.length > 0 ? (
          <div className="space-y-6">
            {reviewsQuery.data.map((review) => (
              <Card key={review.id}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-lg">{review.user.name}</CardTitle>
                      <p className="text-sm text-muted-foreground">
                        {review.user.isKYCVerified ? "✓ KYC Verified" : "Not KYC Verified"} • 
                        {new Date(review.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-muted-foreground">Overall:</span>
                      <span className={`px-2 py-1 rounded text-xs font-medium ${
                        review.overallSentiment 
                          ? "bg-green-100 text-green-700" 
                          : "bg-red-100 text-red-700"
                      }`}>
                        {review.overallSentiment ? "Positive" : "Negative"}
                      </span>
                    </div>
                  </div>
                </CardHeader>
                {review.overallComments && (
                  <CardContent>
                    <div className="flex items-start gap-3">
                      <p className="text-sm leading-relaxed flex-1">{review.overallComments}</p>
                      <ValidationBadge
                        isRelevant={review.overallCommentsRelevant ?? true}
                        relevanceScore={review.overallRelevanceScore ?? 5}
                        confidence={review.overallValidationReason ? 80 : 0}
                        reason={review.overallValidationReason ?? undefined}
                      />
                    </div>
                  </CardContent>
                )}
              </Card>
            ))}
          </div>
        ) : (
          <Card>
            <CardContent className="text-center py-12">
              <p className="text-muted-foreground">No reviews yet. Be the first to review this project!</p>
              {project.isApprovedForVoting && (
                <Button asChild className="mt-4">
                  <Link to={`/submit-review?projectId=${project.id}`}>
                    Submit First Review
                  </Link>
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}