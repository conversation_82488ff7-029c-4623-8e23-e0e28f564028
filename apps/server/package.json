{"name": "server", "version": "0.1.0", "private": true, "main": "src/types/index.ts", "types": "src/types/index.ts", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "check-types": "tsc --noEmit", "db:types": "supabase gen types --lang=typescript --local > src/types/supabase.ts", "db:seed": "tsx src/lib/seed.ts"}, "dependencies": {"@supabase/supabase-js": "^2.50.0", "@trpc/client": "^11.0.0", "@trpc/server": "^11.0.0", "dotenv": "^16.5.0", "next": "15.3.0", "openai": "^5.3.0", "zod": "^3.25.42"}, "devDependencies": {"@types/node": "^20", "@types/react": "^19", "tsx": "^4.20.3", "typescript": "^5"}}