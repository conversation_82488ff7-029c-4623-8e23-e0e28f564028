export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      ai_analyses: {
        Row: {
          analysis: string | null
          analysis_version: string
          community_communication_score: number
          confidence: number
          created_at: string
          id: string
          overall_score: number
          project_fundamentals_score: number
          project_id: string
          reasoning: string | null
          recommendations: string[]
          strengths: string[]
          team_governance_score: number
          technology_execution_score: number
          token_utility_tokenomics_score: number
          transparency_doc_score: number
          updated_at: string
          weaknesses: string[]
        }
        Insert: {
          analysis?: string | null
          analysis_version?: string
          community_communication_score?: number
          confidence?: number
          created_at?: string
          id?: string
          overall_score?: number
          project_fundamentals_score?: number
          project_id: string
          reasoning?: string | null
          recommendations?: string[]
          strengths?: string[]
          team_governance_score?: number
          technology_execution_score?: number
          token_utility_tokenomics_score?: number
          transparency_doc_score?: number
          updated_at?: string
          weaknesses?: string[]
        }
        Update: {
          analysis?: string | null
          analysis_version?: string
          community_communication_score?: number
          confidence?: number
          created_at?: string
          id?: string
          overall_score?: number
          project_fundamentals_score?: number
          project_id?: string
          reasoning?: string | null
          recommendations?: string[]
          strengths?: string[]
          team_governance_score?: number
          technology_execution_score?: number
          token_utility_tokenomics_score?: number
          transparency_doc_score?: number
          updated_at?: string
          weaknesses?: string[]
        }
        Relationships: [
          {
            foreignKeyName: "ai_analyses_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: true
            referencedRelation: "projects"
            referencedColumns: ["id"]
          },
        ]
      }
      projects: {
        Row: {
          challenge_intro: string
          created_at: string
          deck_url: string | null
          description: string
          id: string
          image_url: string | null
          is_approved_for_voting: boolean
          social_urls: Json | null
          title: string
          updated_at: string
          website_url: string | null
          whitepaper_url: string | null
        }
        Insert: {
          challenge_intro: string
          created_at?: string
          deck_url?: string | null
          description: string
          id?: string
          image_url?: string | null
          is_approved_for_voting?: boolean
          social_urls?: Json | null
          title: string
          updated_at?: string
          website_url?: string | null
          whitepaper_url?: string | null
        }
        Update: {
          challenge_intro?: string
          created_at?: string
          deck_url?: string | null
          description?: string
          id?: string
          image_url?: string | null
          is_approved_for_voting?: boolean
          social_urls?: Json | null
          title?: string
          updated_at?: string
          website_url?: string | null
          whitepaper_url?: string | null
        }
        Relationships: []
      }
      review_responses: {
        Row: {
          ai_confidence: number
          created_at: string
          dimension: Database["public"]["Enums"]["review_dimension"]
          feedback: string | null
          feedback_relevant: boolean
          id: string
          question_index: number
          relevance_score: number
          review_id: string
          validation_reason: string | null
          vote: boolean
        }
        Insert: {
          ai_confidence?: number
          created_at?: string
          dimension: Database["public"]["Enums"]["review_dimension"]
          feedback?: string | null
          feedback_relevant?: boolean
          id?: string
          question_index: number
          relevance_score?: number
          review_id: string
          validation_reason?: string | null
          vote: boolean
        }
        Update: {
          ai_confidence?: number
          created_at?: string
          dimension?: Database["public"]["Enums"]["review_dimension"]
          feedback?: string | null
          feedback_relevant?: boolean
          id?: string
          question_index?: number
          relevance_score?: number
          review_id?: string
          validation_reason?: string | null
          vote?: boolean
        }
        Relationships: [
          {
            foreignKeyName: "review_responses_review_id_fkey"
            columns: ["review_id"]
            isOneToOne: false
            referencedRelation: "reviews"
            referencedColumns: ["id"]
          },
        ]
      }
      reviews: {
        Row: {
          created_at: string
          id: string
          overall_comments: string | null
          overall_comments_relevant: boolean
          overall_relevance_score: number
          overall_sentiment: boolean
          overall_validation_reason: string | null
          project_id: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          overall_comments?: string | null
          overall_comments_relevant?: boolean
          overall_relevance_score?: number
          overall_sentiment: boolean
          overall_validation_reason?: string | null
          project_id: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          overall_comments?: string | null
          overall_comments_relevant?: boolean
          overall_relevance_score?: number
          overall_sentiment?: boolean
          overall_validation_reason?: string | null
          project_id?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "reviews_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "reviews_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      users: {
        Row: {
          auth_user_id: string
          created_at: string
          email: string
          id: string
          is_kyc_verified: boolean
          name: string
          updated_at: string
        }
        Insert: {
          auth_user_id: string
          created_at?: string
          email: string
          id?: string
          is_kyc_verified?: boolean
          name: string
          updated_at?: string
        }
        Update: {
          auth_user_id?: string
          created_at?: string
          email?: string
          id?: string
          is_kyc_verified?: boolean
          name?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "users_auth_user_id_fkey"
            columns: ["auth_user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      review_dimension:
        | "PROJECT_FUNDAMENTALS"
        | "TEAM_GOVERNANCE"
        | "TRANSPARENCY_DOCUMENTATION"
        | "TECHNOLOGY_EXECUTION"
        | "COMMUNITY_COMMUNICATION"
        | "TOKEN_UTILITY_TOKENOMICS"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}