import {
  publicProcedure,
  router,
} from "../lib/trpc";
import { projectsRouter } from "./projects";
import { reviewsRouter } from "./reviews";
import { usersRouter } from "./users";

export const appRouter = router({
  healthCheck: publicProcedure.query(() => {
    return "OK";
  }),
  projects: projectsRouter,
  reviews: reviewsRouter,
  users: usersRouter,
});

export type AppRouter = typeof appRouter;
