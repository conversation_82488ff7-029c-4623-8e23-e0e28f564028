import { z } from "zod";
import { publicProcedure, router } from "../lib/trpc";
import { REVIEW_DIMENSIONS, calculateDimensionScore } from "../lib/review-constants";
import { getApprovedProjects, getProjectById } from "../lib/supabase";

export const projectsRouter = router({
  list: publicProcedure.query(async ({ ctx }) => {
    return getApprovedProjects();
  }),

  getById: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const project = await getProjectById(input.id);

      if (!project) {
        throw new Error("Project not found");
      }

      // Calculate aggregated dimension scores
      const dimensionScores = REVIEW_DIMENSIONS.map(dimension => {
        const allResponses = project.reviews?.flatMap((review: any) =>
          review.review_responses?.filter((response: any) => response.dimension === dimension.key) || []
        ) || [];

        const score = calculateDimensionScore(allResponses);
        
        return {
          dimension: dimension.key,
          title: dimension.title,
          score,
          totalReviews: project.reviews?.length || 0,
        };
      });

      // Create AI dimension scores if AI analysis exists
      const aiAnalysis = project.ai_analyses;
      const aiDimensionScores = aiAnalysis ? [
        {
          dimension: "PROJECT_FUNDAMENTALS",
          title: "Project Fundamentals",
          score: aiAnalysis.project_fundamentals_score,
        },
        {
          dimension: "TEAM_GOVERNANCE",
          title: "Team & Governance",
          score: aiAnalysis.team_governance_score,
        },
        {
          dimension: "TRANSPARENCY_DOCUMENTATION",
          title: "Transparency & Documentation",
          score: aiAnalysis.transparency_doc_score,
        },
        {
          dimension: "TECHNOLOGY_EXECUTION",
          title: "Technology & Execution",
          score: aiAnalysis.technology_execution_score,
        },
        {
          dimension: "COMMUNITY_COMMUNICATION",
          title: "Community & Communication",
          score: aiAnalysis.community_communication_score,
        },
        {
          dimension: "TOKEN_UTILITY_TOKENOMICS",
          title: "Token Utility & Tokenomics",
          score: aiAnalysis.token_utility_tokenomics_score,
        },
      ] : null;

      return {
        ...project,
        dimensionScores,
        aiDimensionScores,
        totalReviews: project.reviews?.length || 0,
      };
    }),

  create: publicProcedure
    .input(z.object({
      title: z.string().min(1),
      description: z.string().min(1),
      imageUrl: z.string().url().optional(),
      websiteUrl: z.string().url().optional(),
      deckUrl: z.string().url().optional(),
      whitepaperUrl: z.string().url().optional(),
      socialUrls: z.record(z.string()).optional(),
      challengeIntro: z.string().min(1),
    }))
    .mutation(async ({ ctx, input }) => {
      // Only admins can create projects
      if (!ctx.isAdmin) {
        throw new Error("Admin access required to create projects");
      }

      const { data, error } = await ctx.supabase
        .from('projects')
        .insert({
          title: input.title,
          description: input.description,
          image_url: input.imageUrl,
          website_url: input.websiteUrl,
          deck_url: input.deckUrl,
          whitepaper_url: input.whitepaperUrl,
          social_urls: input.socialUrls || {},
          challenge_intro: input.challengeIntro,
          is_approved_for_voting: false, // Requires approval
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    }),
});