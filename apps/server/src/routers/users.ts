import { z } from "zod";
import { publicProcedure, router } from "../lib/trpc";
import { AuthenticationError, ValidationError } from "../lib/supabase";

export const usersRouter = router({
  getCurrentUser: publicProcedure.query(async ({ ctx }) => {
    if (!ctx.userProfile) {
      return null;
    }
    
    return ctx.userProfile;
  }),

  updateProfile: publicProcedure
    .input(z.object({
      name: z.string().min(1).optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      if (!ctx.userProfile) {
        throw new AuthenticationError("Authentication required");
      }

      const { data, error } = await ctx.supabase
        .from('users')
        .update({
          name: input.name,
        })
        .eq('id', ctx.userProfile.id)
        .select()
        .single();

      if (error) throw error;
      return data;
    }),

  updateKYCStatus: publicProcedure
    .input(z.object({
      userId: z.string(),
      isKYCVerified: z.boolean(),
    }))
    .mutation(async ({ ctx, input }) => {
      // Only admins can update KYC status
      if (!ctx.isAdmin) {
        throw new ValidationError("Admin access required to update KYC status");
      }

      const { data, error } = await ctx.supabase
        .from('users')
        .update({
          is_kyc_verified: input.isKYCVerified,
        })
        .eq('id', input.userId)
        .select()
        .single();

      if (error) throw error;
      return data;
    }),

  getAllUsers: publicProcedure.query(async ({ ctx }) => {
    // Only admins can view all users
    if (!ctx.isAdmin) {
      throw new ValidationError("Admin access required to view all users");
    }

    const { data, error } = await ctx.supabase
      .from('users')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  }),
});