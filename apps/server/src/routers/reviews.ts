import { z } from "zod";
import { publicProcedure, router } from "../lib/trpc";
import { ReviewDimension, getDimensionInfo } from "../lib/review-constants";
import { validateFeedbackRelevance, validateOverallComments } from "../lib/ai-validation";
import { 
  getReviewsByProject, 
  createReview, 
  createReviewResponses,
  AuthenticationError,
  ValidationError 
} from "../lib/supabase";
import type { Enums } from "../types/database";

const reviewResponseSchema = z.object({
  dimension: z.nativeEnum(ReviewDimension),
  questionIndex: z.number().min(0),
  vote: z.boolean(),
  feedback: z.string().optional(),
});

export const reviewsRouter = router({
  create: publicProcedure
    .input(z.object({
      projectId: z.string(),
      overallSentiment: z.boolean(),
      overallComments: z.string().optional(),
      responses: z.array(reviewResponseSchema),
    }))
    .mutation(async ({ ctx, input }) => {
      // Require authentication
      if (!ctx.userProfile) {
        throw new AuthenticationError("Authentication required to submit reviews");
      }

      // Check KYC verification
      if (!ctx.userProfile.is_kyc_verified) {
        throw new ValidationError("KYC verification required to submit reviews");
      }

      console.log("📝 Creating review for user:", ctx.userProfile.email, "projectId:", input.projectId);

      const isDebugMode = process.env.DEBUG_MODE === "true";
      
      // Check if user already reviewed this project (skip in debug mode)
      if (!isDebugMode) {
        const { data: existingReview } = await ctx.supabase
          .from('reviews')
          .select('id')
          .eq('user_id', ctx.userProfile.id)
          .eq('project_id', input.projectId)
          .single();

        if (existingReview) {
          throw new ValidationError("You have already reviewed this project");
        }
      } else {
        console.log("🐛 Debug mode: Allowing multiple reviews from same user");
      }

      // Get project details for AI validation
      const { data: project, error: projectError } = await ctx.supabase
        .from('projects')
        .select('id, title, description')
        .eq('id', input.projectId)
        .single();

      if (projectError || !project) {
        throw new ValidationError("Project not found");
      }

      // Validate overall comments with AI if provided
      let overallValidation = null;
      if (input.overallComments && input.overallComments.trim().length > 0) {
        try {
          overallValidation = await validateOverallComments(
            input.overallComments,
            project.title,
            project.description
          );
        } catch (error) {
          console.error("AI validation failed for overall comments:", error);
        }
      }

      // Validate feedback for each response with AI
      const responsesWithValidation = await Promise.all(
        input.responses.map(async (response) => {
          let validation = null;
          
          if (response.feedback && response.feedback.trim().length > 0) {
            try {
              const dimensionInfo = getDimensionInfo(response.dimension);
              validation = await validateFeedbackRelevance(
                response.feedback,
                dimensionInfo.title,
                dimensionInfo.questions,
                project.title,
                project.description
              );
            } catch (error) {
              console.error(`AI validation failed for dimension ${response.dimension}:`, error);
            }
          }

          return {
            ...response,
            validation,
          };
        })
      );

      // Create review
      const review = await createReview({
        userId: ctx.userProfile.id,
        projectId: input.projectId,
        overallSentiment: input.overallSentiment,
        overallComments: input.overallComments,
      });

      // Update review with validation results if available
      if (overallValidation) {
        await ctx.supabase
          .from('reviews')
          .update({
            overall_comments_relevant: overallValidation.isRelevant,
            overall_relevance_score: overallValidation.relevanceScore,
            overall_validation_reason: overallValidation.reasoning,
          })
          .eq('id', review.id);
      }

      // Create review responses
      await createReviewResponses(
        responsesWithValidation.map(response => ({
          reviewId: review.id,
          dimension: response.dimension as Enums<'review_dimension'>,
          questionIndex: response.questionIndex,
          vote: response.vote,
          feedback: response.feedback,
          feedbackRelevant: response.validation?.isRelevant,
          relevanceScore: response.validation?.relevanceScore,
          validationReason: response.validation?.reasoning,
          aiConfidence: response.validation?.confidence,
        }))
      );

      return review;
    }),

  getByProject: publicProcedure
    .input(z.object({ projectId: z.string() }))
    .query(async ({ ctx, input }) => {
      return getReviewsByProject(input.projectId);
    }),

  getUserReviewForProject: publicProcedure
    .input(z.object({ projectId: z.string() }))
    .query(async ({ ctx, input }) => {
      if (!ctx.userProfile) {
        return null;
      }

      const { data: review } = await ctx.supabase
        .from('reviews')
        .select(`
          *,
          review_responses(*)
        `)
        .eq('user_id', ctx.userProfile.id)
        .eq('project_id', input.projectId)
        .single();

      return review;
    }),
});