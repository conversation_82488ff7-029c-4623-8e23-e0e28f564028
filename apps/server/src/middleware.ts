import { NextResponse } from "next/server";

export function middleware(request: Request) {
  // Handle CORS pre-flight requests from any path early.
  if (request.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Credentials': 'true',
        'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*',
        'Access-Control-Allow-Methods': 'GET,POST,OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, x-user-id',
      },
    })
  }

  const res = NextResponse.next()

  res.headers.append('Access-Control-Allow-Credentials', "true")
  // Allow explicit origin from env during dev; otherwise fall back to '*'
  res.headers.append(
    'Access-Control-Allow-Origin',
    process.env.CORS_ORIGIN || '*'
  )
  res.headers.append('Access-Control-Allow-Methods', 'GET,POST,OPTIONS')
  res.headers.append(
    'Access-Control-Allow-Headers',
    'Content-Type, Authorization, x-user-id'
  )

  return res
}

export const config = {
  matcher: '/:path*',
}
