import { OpenAI } from "openai";

const openai = new OpenAI({
  baseURL: "https://openrouter.ai/api/v1",
  apiKey: process.env.OPENROUTER_API_KEY,
});

export interface ValidationResult {
  isRelevant: boolean;
  relevanceScore: number; // 1-10
  reasoning: string;
  confidence: number; // 0-100
}

export async function validateFeedbackRelevance(
  feedback: string,
  dimensionTitle: string,
  dimensionQuestions: string[],
  projectTitle: string,
  projectDescription: string
): Promise<ValidationResult> {
  const prompt = `You are an expert reviewer analyzing user feedback for blockchain project reviews.

PROJECT CONTEXT:
- Project: ${projectTitle}
- Description: ${projectDescription}

REVIEW DIMENSION:
- Dimension: ${dimensionTitle}
- Questions being addressed: ${dimensionQuestions.join("; ")}

USER FEEDBACK TO VALIDATE:
"${feedback}"

TASK: Evaluate if this feedback is relevant and constructive for the given dimension and project.

CRITERIA FOR RELEVANT FEEDBACK:
1. Addresses aspects covered by the dimension questions
2. Provides specific insights about the project
3. Shows understanding of the project context
4. Offers constructive analysis (positive or negative)
5. Avoids spam, gibberish, or completely off-topic content

RESPOND WITH A JSON OBJECT:
{
  "isRelevant": boolean,
  "relevanceScore": number (1-10, where 1 = completely irrelevant, 10 = perfectly relevant),
  "reasoning": "Brief explanation of why this feedback is/isn't relevant",
  "confidence": number (0-100, how confident you are in this assessment)
}

Examples of RELEVANT feedback:
- "The team has strong blockchain experience but lacks marketing expertise mentioned in their whitepaper"
- "The tokenomics seem unsustainable due to high inflation rates without clear burn mechanisms"
- "Technical documentation is comprehensive but missing security audit information"

Examples of IRRELEVANT feedback:
- "Nice project" (too generic)
- Random text or gibberish
- Comments about unrelated topics
- Spam or promotional content`;

  try {
    const response = await openai.chat.completions.create({
      model: "google/gemini-2.5-flash-preview-05-20",
      messages: [
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.1,
      max_tokens: 500,
      response_format: { type: "json_object" }
    });

    const content = response.choices[0]?.message?.content;
    if (!content) {
      throw new Error("No response from AI validation service");
    }

    // Parse JSON response
    const result = JSON.parse(content) as ValidationResult;
    
    // Validate the response structure
    if (
      typeof result.isRelevant !== "boolean" ||
      typeof result.relevanceScore !== "number" ||
      typeof result.reasoning !== "string" ||
      typeof result.confidence !== "number"
    ) {
      throw new Error("Invalid response format from AI validation service");
    }

    // Ensure scores are within valid ranges
    result.relevanceScore = Math.max(1, Math.min(10, result.relevanceScore));
    result.confidence = Math.max(0, Math.min(100, result.confidence));

    return result;
  } catch (error) {
    console.error("AI validation error:", error);
    
    // Fallback: If AI service fails, assume content is relevant but with low confidence
    return {
      isRelevant: true,
      relevanceScore: 5,
      reasoning: "AI validation service unavailable - manual review recommended",
      confidence: 0,
    };
  }
}

export async function validateOverallComments(
  comments: string,
  projectTitle: string,
  projectDescription: string
): Promise<ValidationResult> {
  const prompt = `You are analyzing overall comments for a blockchain project review.

PROJECT CONTEXT:
- Project: ${projectTitle}
- Description: ${projectDescription}

OVERALL COMMENTS TO VALIDATE:
"${comments}"

TASK: Evaluate if these overall comments provide meaningful insight about the project.

CRITERIA FOR RELEVANT COMMENTS:
1. Summarizes key strengths or weaknesses
2. Provides project-specific insights
3. Shows understanding of the project's goals
4. Offers constructive overall assessment
5. Avoids generic statements or spam

RESPOND WITH A JSON OBJECT:
{
  "isRelevant": boolean,
  "relevanceScore": number (1-10, where 1 = completely irrelevant, 10 = perfectly relevant),
  "reasoning": "Brief explanation",
  "confidence": number (0-100)
}`;

  try {
    const response = await openai.chat.completions.create({
      model: "google/gemini-2.5-flash-preview-05-20",
      messages: [
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.1,
      max_tokens: 500,
      response_format: { type: "json_object" }
    });

    const content = response.choices[0]?.message?.content;
    if (!content) {
      throw new Error("No response from AI validation service");
    }

    const result = JSON.parse(content) as ValidationResult;
    
    // Validate and clamp scores
    result.relevanceScore = Math.max(1, Math.min(10, result.relevanceScore));
    result.confidence = Math.max(0, Math.min(100, result.confidence));

    return result;
  } catch (error) {
    console.error("AI validation error:", error);
    
    return {
      isRelevant: true,
      relevanceScore: 5,
      reasoning: "AI validation service unavailable - manual review recommended",
      confidence: 0,
    };
  }
}