import type { NextRequest } from "next/server";
import { supabaseAdmin, getUserByAuthId, isUserAdmin } from "./supabase";

export async function createContext(req: NextRequest) {
  // Get auth token from request
  const authHeader = req.headers.get('authorization');
  const token = authHeader?.replace('Bearer ', '');

  let user = null;
  let userProfile = null;
  let isAdmin = false;

  if (token) {
    try {
      // Verify JWT token with Supabase
      const { data: { user: authUser }, error } = await supabaseAdmin.auth.getUser(token);
      
      if (authUser && !error) {
        user = authUser;
        
        // Get user profile from our users table
        try {
          userProfile = await getUserByAuthId(authUser.id);
          isAdmin = userProfile ? isUserAdmin(userProfile.email) : false;
        } catch (profileError) {
          console.warn('Could not fetch user profile:', profileError);
        }
      }
    } catch (authError) {
      console.warn('Auth error:', authError);
    }
  }

  // Fallback to simple user simulation for development (remove in production)
  if (!user && process.env.NODE_ENV === 'development') {
    const devUserId = req.headers.get("x-user-id") || "user_1";
    console.log("🔐 Dev mode: Using simulated userId:", devUserId);
    
    // Try to get user by dev ID mapping
    try {
      const devUserMap: Record<string, string> = {
        'user_1': '550e8400-e29b-41d4-a716-446655440001', // Alice - KYC verified
        'user_2': '550e8400-e29b-41d4-a716-446655440002', // Bob - Not KYC verified  
        'admin_1': '550e8400-e29b-41d4-a716-446655440000', // Admin
      };
      
      const profileId = devUserMap[devUserId];
      if (profileId) {
        const { data: profile } = await supabaseAdmin
          .from('users')
          .select('*')
          .eq('id', profileId)
          .single();
        
        if (profile) {
          userProfile = profile;
          isAdmin = isUserAdmin(profile.email);
          // Create mock auth user
          user = {
            id: profile.auth_user_id,
            email: profile.email,
          } as any;
        }
      }
    } catch (error) {
      console.warn('Dev user lookup failed:', error);
    }
  }

  console.log("🔐 tRPC Context created:", { 
    hasUser: !!user, 
    hasProfile: !!userProfile, 
    isAdmin,
    email: userProfile?.email 
  });

  return {
    supabase: supabaseAdmin,
    user,
    userProfile,
    isAdmin,
  };
}

export type Context = Awaited<ReturnType<typeof createContext>>;
