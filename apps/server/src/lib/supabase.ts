import { createClient } from '@supabase/supabase-js'
import type { Database } from '../types/database'

const supabaseUrl = process.env.SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY!

if (!supabaseUrl || !supabaseServiceKey || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

// Admin client with service role key for server-side operations
export const supabaseAdmin = createClient<Database>(
  supabaseUrl,
  supabaseServiceKey,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

// Client with anon key for user-level operations
export const supabase = createClient<Database>(
  supabaseUrl,
  supabaseAnonKey
)

// Database types
export type Tables<T extends keyof Database['public']['Tables']> = 
  Database['public']['Tables'][T]['Row']

export type Enums<T extends keyof Database['public']['Enums']> = 
  Database['public']['Enums'][T]

// User operations
export const getUserByAuthId = async (authUserId: string) => {
  const { data, error } = await supabaseAdmin
    .from('users')
    .select('*')
    .eq('auth_user_id', authUserId)
    .single()

  if (error) throw error
  return data
}

export const createUser = async (userData: {
  email: string
  name: string
  authUserId: string
  isKycVerified?: boolean
}) => {
  const { data, error } = await supabaseAdmin
    .from('users')
    .insert({
      email: userData.email,
      name: userData.name,
      auth_user_id: userData.authUserId,
      is_kyc_verified: userData.isKycVerified || false
    })
    .select()
    .single()

  if (error) throw error
  return data
}

// Project operations
export const getApprovedProjects = async () => {
  const { data, error } = await supabase
    .from('projects')
    .select('*')
    .eq('is_approved_for_voting', true)
    .order('created_at', { ascending: false })

  if (error) throw error
  return data
}

export const getProjectById = async (projectId: string) => {
  const { data, error } = await supabase
    .from('projects')
    .select(`
      *,
      reviews (
        *,
        users (name, is_kyc_verified),
        review_responses (*)
      ),
      ai_analyses (*)
    `)
    .eq('id', projectId)
    .single()

  if (error) throw error
  return data
}

// Review operations
export const getReviewsByProject = async (projectId: string) => {
  const { data, error } = await supabase
    .from('reviews')
    .select(`
      *,
      users (name, is_kyc_verified),
      review_responses (*)
    `)
    .eq('project_id', projectId)
    .order('created_at', { ascending: false })

  if (error) throw error
  return data
}

export const createReview = async (reviewData: {
  userId: string
  projectId: string
  overallSentiment: boolean
  overallComments?: string
}) => {
  const { data, error } = await supabaseAdmin
    .from('reviews')
    .insert({
      user_id: reviewData.userId,
      project_id: reviewData.projectId,
      overall_sentiment: reviewData.overallSentiment,
      overall_comments: reviewData.overallComments
    })
    .select()
    .single()

  if (error) throw error
  return data
}

export const createReviewResponses = async (responses: Array<{
  reviewId: string
  dimension: Enums<'review_dimension'>
  questionIndex: number
  vote: boolean
  feedback?: string
  feedbackRelevant?: boolean
  relevanceScore?: number
  validationReason?: string
  aiConfidence?: number
}>) => {
  const { data, error } = await supabaseAdmin
    .from('review_responses')
    .insert(responses.map(response => ({
      review_id: response.reviewId,
      dimension: response.dimension,
      question_index: response.questionIndex,
      vote: response.vote,
      feedback: response.feedback,
      feedback_relevant: response.feedbackRelevant ?? true,
      relevance_score: response.relevanceScore ?? 50,
      validation_reason: response.validationReason,
      ai_confidence: response.aiConfidence ?? 0
    })))
    .select()

  if (error) throw error
  return data
}

// AI Analysis operations
export const getAIAnalysisByProject = async (projectId: string) => {
  const { data, error } = await supabase
    .from('ai_analyses')
    .select('*')
    .eq('project_id', projectId)
    .single()

  if (error && error.code !== 'PGRST116') throw error // Ignore "not found" errors
  return data
}

export const createOrUpdateAIAnalysis = async (analysisData: {
  projectId: string
  projectFundamentalsScore: number
  teamGovernanceScore: number
  transparencyDocScore: number
  technologyExecutionScore: number
  communityCommunicationScore: number
  tokenUtilityTokenomicsScore: number
  overallScore: number
  analysis?: string
  reasoning?: string
  strengths?: string[]
  weaknesses?: string[]
  recommendations?: string[]
  confidence?: number
  analysisVersion?: string
}) => {
  const { data, error } = await supabaseAdmin
    .from('ai_analyses')
    .upsert({
      project_id: analysisData.projectId,
      project_fundamentals_score: analysisData.projectFundamentalsScore,
      team_governance_score: analysisData.teamGovernanceScore,
      transparency_doc_score: analysisData.transparencyDocScore,
      technology_execution_score: analysisData.technologyExecutionScore,
      community_communication_score: analysisData.communityCommunicationScore,
      token_utility_tokenomics_score: analysisData.tokenUtilityTokenomicsScore,
      overall_score: analysisData.overallScore,
      analysis: analysisData.analysis,
      reasoning: analysisData.reasoning,
      strengths: analysisData.strengths || [],
      weaknesses: analysisData.weaknesses || [],
      recommendations: analysisData.recommendations || [],
      confidence: analysisData.confidence || 0,
      analysis_version: analysisData.analysisVersion || '1.0'
    })
    .select()
    .single()

  if (error) throw error
  return data
}

// Utility functions
export const validateUserExists = async (authUserId: string) => {
  try {
    const user = await getUserByAuthId(authUserId)
    return user
  } catch (error) {
    return null
  }
}

export const isUserKycVerified = async (authUserId: string) => {
  const user = await validateUserExists(authUserId)
  return user?.is_kyc_verified || false
}

export const isUserAdmin = (email: string) => {
  return email.endsWith('@ibc.media')
}

// Error types
export class SupabaseError extends Error {
  constructor(message: string, public code?: string) {
    super(message)
    this.name = 'SupabaseError'
  }
}

export class AuthenticationError extends Error {
  constructor(message: string = 'Authentication required') {
    super(message)
    this.name = 'AuthenticationError'
  }
}

export class AuthorizationError extends Error {
  constructor(message: string = 'Insufficient permissions') {
    super(message)
    this.name = 'AuthorizationError'
  }
}

export class ValidationError extends Error {
  constructor(message: string = 'Validation failed') {
    super(message)
    this.name = 'ValidationError'
  }
}