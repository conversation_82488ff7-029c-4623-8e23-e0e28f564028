#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { createClient } from '@supabase/supabase-js';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Environment variables
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
  console.error('❌ Missing required environment variables:');
  console.error('   SUPABASE_URL');
  console.error('   SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

const logsDir = path.join(__dirname, '../logs');
const exportDir = path.join(__dirname, '../data');

async function validateTableStructure() {
  console.log('🔍 Validating table structure...');
  
  const tables = ['users', 'projects', 'reviews', 'review_responses', 'ai_analyses'];
  const validationResults = {};
  
  for (const table of tables) {
    try {
      // Check if table exists and get sample data
      const { data, error, count } = await supabase
        .from(table)
        .select('*', { count: 'exact' })
        .limit(1);
      
      if (error) {
        validationResults[table] = {
          exists: false,
          error: error.message,
          count: 0
        };
      } else {
        validationResults[table] = {
          exists: true,
          count: count || 0,
          sampleStructure: data?.[0] ? Object.keys(data[0]) : []
        };
      }
    } catch (err) {
      validationResults[table] = {
        exists: false,
        error: err.message,
        count: 0
      };
    }
  }
  
  return validationResults;
}

async function validateDataIntegrity() {
  console.log('🔍 Validating data integrity...');
  
  const integrityResults = {};
  
  try {
    // Check referential integrity
    
    // 1. All reviews should have valid user_id and project_id
    const { data: orphanedReviews, error: reviewsError } = await supabase
      .from('reviews')
      .select(`
        id,
        user_id,
        project_id,
        users!reviews_user_id_fkey(id),
        projects!reviews_project_id_fkey(id)
      `);
    
    if (reviewsError) {
      integrityResults.reviews = { error: reviewsError.message };
    } else {
      const invalidReviews = orphanedReviews?.filter(r => !r.users || !r.projects) || [];
      integrityResults.reviews = {
        total: orphanedReviews?.length || 0,
        invalid: invalidReviews.length,
        valid: (orphanedReviews?.length || 0) - invalidReviews.length
      };
    }
    
    // 2. All review_responses should have valid review_id
    const { data: orphanedResponses, error: responsesError } = await supabase
      .from('review_responses')
      .select(`
        id,
        review_id,
        reviews!review_responses_review_id_fkey(id)
      `);
    
    if (responsesError) {
      integrityResults.reviewResponses = { error: responsesError.message };
    } else {
      const invalidResponses = orphanedResponses?.filter(r => !r.reviews) || [];
      integrityResults.reviewResponses = {
        total: orphanedResponses?.length || 0,
        invalid: invalidResponses.length,
        valid: (orphanedResponses?.length || 0) - invalidResponses.length
      };
    }
    
    // 3. All ai_analyses should have valid project_id
    const { data: orphanedAnalyses, error: analysesError } = await supabase
      .from('ai_analyses')
      .select(`
        id,
        project_id,
        projects!ai_analyses_project_id_fkey(id)
      `);
    
    if (analysesError) {
      integrityResults.aiAnalyses = { error: analysesError.message };
    } else {
      const invalidAnalyses = orphanedAnalyses?.filter(a => !a.projects) || [];
      integrityResults.aiAnalyses = {
        total: orphanedAnalyses?.length || 0,
        invalid: invalidAnalyses.length,
        valid: (orphanedAnalyses?.length || 0) - invalidAnalyses.length
      };
    }
    
  } catch (err) {
    integrityResults.error = err.message;
  }
  
  return integrityResults;
}

async function validateDataCounts() {
  console.log('🔍 Validating data counts against export...');
  
  // Load export summary
  const exportSummaryPath = path.join(exportDir, 'export-summary.json');
  if (!fs.existsSync(exportSummaryPath)) {
    return { error: 'Export summary not found' };
  }
  
  const exportSummary = JSON.parse(fs.readFileSync(exportSummaryPath, 'utf8'));
  const countResults = {};
  
  const tables = [
    { name: 'users', exportKey: 'users' },
    { name: 'projects', exportKey: 'projects' },
    { name: 'reviews', exportKey: 'reviews' },
    { name: 'review_responses', exportKey: 'reviewResponses' },
    { name: 'ai_analyses', exportKey: 'aiAnalyses' }
  ];
  
  for (const table of tables) {
    try {
      const { count, error } = await supabase
        .from(table.name)
        .select('*', { count: 'exact', head: true });
      
      if (error) {
        countResults[table.name] = {
          error: error.message,
          expected: exportSummary.tables[table.exportKey],
          actual: 0,
          match: false
        };
      } else {
        const expected = exportSummary.tables[table.exportKey];
        const actual = count || 0;
        countResults[table.name] = {
          expected,
          actual,
          match: expected === actual,
          difference: actual - expected
        };
      }
    } catch (err) {
      countResults[table.name] = {
        error: err.message,
        expected: exportSummary.tables[table.exportKey],
        actual: 0,
        match: false
      };
    }
  }
  
  return countResults;
}

async function validateEnumTypes() {
  console.log('🔍 Validating enum types...');
  
  try {
    // Test enum values in review_responses
    const { data: enumTest, error } = await supabase
      .from('review_responses')
      .select('dimension')
      .limit(10);
    
    if (error) {
      return { error: error.message };
    }
    
    const expectedEnums = [
      'PROJECT_FUNDAMENTALS',
      'TEAM_GOVERNANCE',
      'TRANSPARENCY_DOCUMENTATION',
      'TECHNOLOGY_EXECUTION',
      'COMMUNITY_COMMUNICATION',
      'TOKEN_UTILITY_TOKENOMICS'
    ];
    
    const actualEnums = [...new Set(enumTest?.map(r => r.dimension) || [])];
    const validEnums = actualEnums.filter(e => expectedEnums.includes(e));
    const invalidEnums = actualEnums.filter(e => !expectedEnums.includes(e));
    
    return {
      expected: expectedEnums,
      found: actualEnums,
      valid: validEnums,
      invalid: invalidEnums,
      allValid: invalidEnums.length === 0
    };
  } catch (err) {
    return { error: err.message };
  }
}

async function validateMigration() {
  console.log('🚀 Starting migration validation...');
  
  try {
    const results = {
      timestamp: new Date().toISOString(),
      tableStructure: await validateTableStructure(),
      dataIntegrity: await validateDataIntegrity(),
      dataCounts: await validateDataCounts(),
      enumTypes: await validateEnumTypes()
    };
    
    // Calculate overall status
    const structureOk = Object.values(results.tableStructure).every(t => t.exists);
    const integrityOk = Object.values(results.dataIntegrity).every(i => 
      !i.error && (i.invalid === undefined || i.invalid === 0)
    );
    const countsOk = Object.values(results.dataCounts).every(c => c.match);
    const enumsOk = results.enumTypes.allValid;
    
    results.overall = {
      success: structureOk && integrityOk && countsOk && enumsOk,
      structure: structureOk,
      integrity: integrityOk,
      counts: countsOk,
      enums: enumsOk
    };
    
    // Save detailed results
    fs.writeFileSync(
      path.join(logsDir, `validation-${Date.now()}.json`),
      JSON.stringify(results, null, 2)
    );
    
    // Print summary
    console.log('📊 Validation Results:');
    console.log(`   Overall Success: ${results.overall.success ? '✅' : '❌'}`);
    console.log(`   Table Structure: ${results.overall.structure ? '✅' : '❌'}`);
    console.log(`   Data Integrity: ${results.overall.integrity ? '✅' : '❌'}`);
    console.log(`   Data Counts: ${results.overall.counts ? '✅' : '❌'}`);
    console.log(`   Enum Types: ${results.overall.enums ? '✅' : '❌'}`);
    
    // Print table counts
    console.log('📊 Table Counts:');
    Object.entries(results.dataCounts).forEach(([table, result]) => {
      if (!result.error) {
        const status = result.match ? '✅' : '❌';
        console.log(`   ${table}: ${result.actual}/${result.expected} ${status}`);
      } else {
        console.log(`   ${table}: Error - ${result.error}`);
      }
    });
    
    // Print integrity results
    console.log('🔗 Referential Integrity:');
    Object.entries(results.dataIntegrity).forEach(([table, result]) => {
      if (!result.error) {
        const status = result.invalid === 0 ? '✅' : '❌';
        console.log(`   ${table}: ${result.valid}/${result.total} valid ${status}`);
      } else {
        console.log(`   ${table}: Error - ${result.error}`);
      }
    });
    
    if (results.overall.success) {
      console.log('🎉 Migration validation passed!');
      
      // Create success marker
      fs.writeFileSync(
        path.join(logsDir, 'migration-success.flag'),
        `Migration completed successfully at ${new Date().toISOString()}`
      );
    } else {
      console.log('❌ Migration validation failed!');
      console.log('📄 Check detailed results in logs directory');
      
      // List specific issues
      if (!results.overall.structure) {
        console.log('🔧 Table structure issues detected');
      }
      if (!results.overall.integrity) {
        console.log('🔧 Data integrity issues detected');
      }
      if (!results.overall.counts) {
        console.log('🔧 Data count mismatches detected');
      }
      if (!results.overall.enums) {
        console.log('🔧 Enum type issues detected');
      }
      
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ Validation failed:', error);
    
    // Log error
    fs.writeFileSync(
      path.join(logsDir, `validation-error-${Date.now()}.log`),
      `Validation failed at ${new Date().toISOString()}\n` +
      `Error: ${error.message}\n` +
      `Stack: ${error.stack}\n`
    );
    
    process.exit(1);
  }
}

validateMigration();