#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const projectRoot = path.join(__dirname, '../..');
const serverPath = path.join(projectRoot, 'apps/server');
const webPath = path.join(projectRoot, 'apps/web');

function runCommand(command, cwd) {
  try {
    console.log(`🔧 Running: ${command} in ${path.relative(projectRoot, cwd)}`);
    execSync(command, { cwd, stdio: 'inherit' });
    return true;
  } catch (error) {
    console.error(`❌ Failed to run: ${command}`);
    console.error(error.message);
    return false;
  }
}

function updateRootPackageJson() {
  const packageJsonPath = path.join(projectRoot, 'package.json');
  
  if (fs.existsSync(packageJsonPath)) {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    // Update scripts to reflect Supabase
    packageJson.scripts = {
      ...packageJson.scripts,
      'db:push': 'turbo -F server supabase:start && turbo -F server db:types',
      'db:studio': 'turbo -F server supabase:start',
      'db:generate': 'turbo -F server db:types',
      'db:migrate': 'turbo -F server supabase:reset',
      'supabase:start': 'turbo -F server supabase:start',
      'supabase:stop': 'turbo -F server supabase:stop',
      'supabase:status': 'turbo -F server supabase:status'
    };
    
    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
    console.log('✅ Updated root package.json scripts');
  }
}

function updateWebAppTrpcClient() {
  const trpcClientPath = path.join(webPath, 'src/utils/trpc.ts');
  
  if (fs.existsSync(trpcClientPath)) {
    const content = fs.readFileSync(trpcClientPath, 'utf8');
    
    // Update any Prisma-specific types if they exist
    const updatedContent = content
      .replace(/PrismaClient/g, 'SupabaseClient')
      .replace(/prisma/g, 'supabase')
      .replace(/\@prisma\/client/g, '@supabase/supabase-js');
    
    if (content !== updatedContent) {
      fs.writeFileSync(trpcClientPath, updatedContent);
      console.log('✅ Updated web app tRPC client');
    }
  }
}

function createSupabaseConfig() {
  const configPath = path.join(serverPath, 'supabase/config.toml');
  const configDir = path.dirname(configPath);
  
  // Create supabase directory if it doesn't exist
  if (!fs.existsSync(configDir)) {
    fs.mkdirSync(configDir, { recursive: true });
  }
  
  const configContent = `# A string used to distinguish different Supabase projects on the same host. Defaults to the
# working directory name when running \`supabase init\`.
project_id = "ibc-cie"

[api]
enabled = true
# Port to use for the API URL.
port = 54321
# Schemas to expose in your API. Tables, views and stored procedures in this schema will get API
# endpoints. public and storage are always included.
schemas = ["public", "storage", "graphql_public"]
# Extra schemas to add to the search_path of every request. public is always included.
extra_search_path = ["public", "extensions"]
# The maximum number of rows returns from a table operation. Applies to select, insert, update
# and delete operations. Unlimited if not set.
max_rows = 1000

[db]
# Port to use for the local database URL.
port = 54322
# Port used by db diff command to initialize the shadow database.
shadow_port = 54320
# The database major version to use. This has to be the same as your remote database's. Run \`SHOW
# server_version\` on the remote database to check.
major_version = 15

[db.pooler]
enabled = false
# Port to use for the local connection pooler.
port = 54329
# Specifies when a server connection can be reused by other clients.
# Configure one of the supported pooler modes: \`transaction\`, \`session\`.
pool_mode = "transaction"
# How many server connections to allow per user/database pair.
default_pool_size = 20
# Maximum number of client connections allowed.
max_client_conn = 100

[realtime]
enabled = true
# Bind realtime via either IPv4 or IPv6. (default: IPv6)
# ip_version = "IPv6"

[studio]
enabled = true
# Port to use for Supabase Studio.
port = 54323
# External URL of the API server that frontend connects to.
api_url = "http://localhost:54321"

# Email testing server. Emails sent with the local dev setup are not actually sent - rather, they
# are monitored, and you can view the emails that would have been sent from the web interface.
[inbucket]
enabled = true
# Port to use for the email testing server web interface.
port = 54324
# Uncomment to expose additional ports for testing user applications that send emails.
# smtp_port = 54325
# pop3_port = 54326

[storage]
enabled = true
# The maximum file size allowed (e.g. "5MB", "500KB").
file_size_limit = "50MiB"

[auth]
enabled = true
# The base URL of your website. Used as an allow-list for redirects and for constructing URLs used
# in emails.
site_url = "http://localhost:3000"
# A list of *exact* URLs that auth providers are permitted to redirect to post authentication.
additional_redirect_urls = ["https://localhost:3000"]
# How long tokens are valid for, in seconds. Defaults to 3600 (1 hour), maximum 604800 (1 week).
jwt_expiry = 3600
# If disabled, the refresh token will never expire.
enable_refresh_token_rotation = true
# Allows refresh tokens to be reused after expiry, up to the specified interval in seconds.
# Requires enable_refresh_token_rotation = true.
refresh_token_reuse_interval = 10
# Allow/disallow new user signups to your project.
enable_signup = true

[auth.email]
# Allow/disallow new user signups via email to your project.
enable_signup = true
# If enabled, a user will be required to confirm any email change on both the old, and new email
# addresses. If disabled, only the new email is required to confirm.
double_confirm_changes = true
# If enabled, users need to confirm their email address before signing in.
enable_confirmations = false

# Uncomment to customize email template
# [auth.email.template.invite]
# subject = "You have been invited"
# content_path = "./supabase/templates/invite.html"

[auth.sms]
# Allow/disallow new user signups via SMS to your project.
enable_signup = true
# If enabled, users need to confirm their phone number before signing in.
enable_confirmations = false

# Configure one of the supported SMS providers: \`twilio\`, \`messagebird\`, \`textlocal\`, \`vonage\`.
[auth.sms.twilio]
enabled = false
account_sid = ""
message_service_sid = ""
# DO NOT commit your Twilio auth token to git. Use environment variable substitution instead:
auth_token = "env(SUPABASE_AUTH_SMS_TWILIO_AUTH_TOKEN)"

# Use pre-defined map of phone number to OTP for testing.
[auth.sms.test_otp]
# ********** = "123456"

[analytics]
enabled = false
port = 54327
vector_port = 54328
# Configure one of the supported backends: \`postgres\`, \`bigquery\`.
backend = "postgres"

[functions]
# A mapping of function names to their properties
# You can set properties like \`verify_jwt\` and \`import_map\` here
[functions.hello-world]
verify_jwt = false
`;
  
  fs.writeFileSync(configPath, configContent);
  console.log('✅ Created Supabase config.toml');
}

function createSupabaseEnv() {
  const envPath = path.join(serverPath, 'supabase/.env.example');
  
  const envContent = `# Supabase Environment Variables
SUPABASE_URL=http://localhost:54321
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU

# Database URLs
DATABASE_URL=postgresql://postgres:postgres@localhost:54322/postgres
DIRECT_URL=postgresql://postgres:postgres@localhost:54322/postgres
`;
  
  fs.writeFileSync(envPath, envContent);
  console.log('✅ Created Supabase .env.example');
}

async function updateDependencies() {
  console.log('📦 Updating dependencies and configuration...');
  
  try {
    // Update root package.json
    updateRootPackageJson();
    
    // Update web app if needed
    updateWebAppTrpcClient();
    
    // Create Supabase configuration
    createSupabaseConfig();
    createSupabaseEnv();
    
    // Install dependencies in server
    console.log('📦 Installing Supabase dependencies in server...');
    if (!runCommand('npm install @supabase/supabase-js@^2.50.0', serverPath)) {
      throw new Error('Failed to install Supabase dependencies');
    }
    
    // Remove Prisma dependencies from server
    console.log('🗑️  Removing Prisma dependencies...');
    runCommand('npm uninstall @prisma/client prisma', serverPath);
    
    // Install Supabase CLI globally if not present
    console.log('🔧 Checking Supabase CLI...');
    try {
      execSync('supabase --version', { stdio: 'pipe' });
      console.log('✅ Supabase CLI already installed');
    } catch {
      console.log('📦 Installing Supabase CLI...');
      if (process.platform === 'darwin') {
        runCommand('brew install supabase/tap/supabase', projectRoot);
      } else {
        console.log('⚠️  Please install Supabase CLI manually: https://supabase.com/docs/guides/cli');
      }
    }
    
    console.log('✅ Dependencies updated successfully!');
    console.log('📝 Next steps:');
    console.log('   1. Initialize Supabase: cd apps/server && supabase init');
    console.log('   2. Start local Supabase: supabase start');
    console.log('   3. Run migrations: supabase db reset');
    console.log('   4. Update your .env file with local Supabase credentials');
    
  } catch (error) {
    console.error('❌ Dependencies update failed:', error);
    process.exit(1);
  }
}

updateDependencies();