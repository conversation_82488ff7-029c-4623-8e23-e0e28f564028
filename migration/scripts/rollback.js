#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const projectRoot = path.join(__dirname, '../..');
const serverPath = path.join(projectRoot, 'apps/server');
const logsDir = path.join(__dirname, '../logs');

function runCommand(command, cwd, ignoreErrors = false) {
  try {
    console.log(`🔧 Running: ${command} in ${path.relative(projectRoot, cwd)}`);
    execSync(command, { cwd, stdio: 'inherit' });
    return true;
  } catch (error) {
    if (!ignoreErrors) {
      console.error(`❌ Failed to run: ${command}`);
      console.error(error.message);
    }
    return false;
  }
}

function restoreFile(filePath, backupContent) {
  try {
    fs.writeFileSync(filePath, backupContent);
    console.log(`✅ Restored: ${path.relative(projectRoot, filePath)}`);
    return true;
  } catch (error) {
    console.error(`❌ Failed to restore: ${filePath}`, error.message);
    return false;
  }
}

function createPrismaFiles() {
  console.log('🔧 Recreating Prisma files...');
  
  // Restore Prisma schema
  const prismaSchemaPath = path.join(serverPath, 'prisma/schema/schema.prisma');
  const prismaSchemaContent = `generator client {
  provider = "prisma-client"
  output   = "../generated"
  moduleFormat = "esm"
}

datasource db {
  provider = "postgres"
  url      = env("DATABASE_URL")
}

model User {
  id            String   @id @default(cuid())
  email         String   @unique
  name          String
  isKYCVerified Boolean  @default(false)
  createdAt     DateTime @default(now())
  
  reviews Review[]
  
  @@map("users")
}

model Project {
  id                   String   @id @default(cuid())
  title                String
  description          String
  imageUrl             String?
  websiteUrl           String?
  deckUrl              String?
  whitepaperUrl        String?
  socialUrls           Json?
  challengeIntro       String
  isApprovedForVoting  Boolean  @default(false)
  createdAt            DateTime @default(now())

  reviews Review[]
  aiAnalysis AIAnalysis?

  @@map("projects")
}

model Review {
  id                      String   @id @default(cuid())
  userId                  String
  projectId               String
  overallSentiment        Boolean
  overallComments         String?
  overallCommentsRelevant Boolean  @default(true)
  overallRelevanceScore   Int      @default(50)
  overallValidationReason String?
  createdAt               DateTime @default(now())
  
  user      User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  project   Project           @relation(fields: [projectId], references: [id], onDelete: Cascade)
  responses ReviewResponse[]
  
  @@unique([userId, projectId])
  @@map("reviews")
}

enum ReviewDimension {
  PROJECT_FUNDAMENTALS
  TEAM_GOVERNANCE
  TRANSPARENCY_DOCUMENTATION
  TECHNOLOGY_EXECUTION
  COMMUNITY_COMMUNICATION
  TOKEN_UTILITY_TOKENOMICS
}

model ReviewResponse {
  id               String          @id @default(cuid())
  reviewId         String
  dimension        ReviewDimension
  questionIndex    Int
  vote             Boolean
  feedback         String?
  feedbackRelevant Boolean         @default(true)
  relevanceScore   Int             @default(50)
  validationReason String?
  aiConfidence     Int             @default(0)

  review Review @relation(fields: [reviewId], references: [id], onDelete: Cascade)

  @@unique([reviewId, dimension, questionIndex])
  @@map("review_responses")
}

model AIAnalysis {
  id                          String   @id @default(cuid())
  projectId                   String   @unique
  projectFundamentalsScore    Int      @default(0)
  teamGovernanceScore         Int      @default(0)
  transparencyDocScore        Int      @default(0)
  technologyExecutionScore    Int      @default(0)
  communityCommunicationScore Int      @default(0)
  tokenUtilityTokenomicsScore Int      @default(0)
  overallScore                Int      @default(0)
  analysis                    String?
  reasoning                   String?
  strengths                   String[]
  weaknesses                  String[]
  recommendations             String[]
  confidence                  Int      @default(0)
  analysisVersion             String   @default("1.0")
  createdAt                   DateTime @default(now())
  updatedAt                   DateTime @updatedAt

  project Project @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@map("ai_analyses")
}`;

  // Ensure Prisma directories exist
  const prismaDir = path.dirname(prismaSchemaPath);
  if (!fs.existsSync(prismaDir)) {
    fs.mkdirSync(prismaDir, { recursive: true });
  }
  
  fs.writeFileSync(prismaSchemaPath, prismaSchemaContent);
  console.log('✅ Restored Prisma schema');
}

function restorePrismaContext() {
  const contextPath = path.join(serverPath, 'src/lib/context.ts');
  
  const contextContent = `import type { NextRequest } from "next/server";
import { PrismaClient } from "../../prisma/generated";

const prisma = new PrismaClient();

export async function createContext(req: NextRequest) {
  // Simple user simulation for now - in a real app this would be from auth
  const userId = req.headers.get("x-user-id") || "user_1";

  console.log("🔐 tRPC Context created with userId:", userId);

  return {
    prisma,
    userId,
  };
}

export type Context = Awaited<ReturnType<typeof createContext>>;`;

  fs.writeFileSync(contextPath, contextContent);
  console.log('✅ Restored Prisma context');
}

function restorePackageJson() {
  const packageJsonPath = path.join(serverPath, 'package.json');
  
  if (fs.existsSync(packageJsonPath)) {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    // Remove Supabase dependencies
    delete packageJson.dependencies['@supabase/supabase-js'];
    
    // Add back Prisma dependencies
    packageJson.dependencies['@prisma/client'] = '^6.9.0';
    packageJson.devDependencies = packageJson.devDependencies || {};
    packageJson.devDependencies['prisma'] = '^6.9.0';
    
    // Restore original scripts
    packageJson.scripts = {
      ...packageJson.scripts,
      'db:push': 'prisma db push --schema ./prisma/schema',
      'db:studio': 'prisma studio',
      'db:generate': 'prisma generate --schema ./prisma/schema',
      'db:migrate': 'prisma migrate dev',
      'db:seed': 'tsx src/lib/seed.ts'
    };
    
    // Remove Supabase scripts
    delete packageJson.scripts['db:types'];
    delete packageJson.scripts['supabase:start'];
    delete packageJson.scripts['supabase:stop'];
    delete packageJson.scripts['supabase:status'];
    delete packageJson.scripts['supabase:reset'];
    
    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
    console.log('✅ Restored package.json');
  }
}

function restoreEnvExample() {
  const envExamplePath = path.join(serverPath, '.env.example');
  
  const envContent = `CORS_ORIGIN=
DATABASE_URL=
OPENROUTER_API_KEY=`;
  
  fs.writeFileSync(envExamplePath, envContent);
  console.log('✅ Restored .env.example');
}

function restoreSeedScript() {
  const seedPath = path.join(serverPath, 'src/lib/seed.ts');
  
  const seedContent = `import "dotenv/config";
import { PrismaClient } from "../../prisma/generated/index.js";

const prisma = new PrismaClient();

async function seed() {
  console.log("🌱 Seeding database...");

  // Create test users
  const users = await Promise.all([
    prisma.user.upsert({
      where: { id: "user_1" },
      update: {},
      create: {
        id: "user_1",
        email: "<EMAIL>",
        name: "John Doe",
        isKYCVerified: true,
      },
    }),
    prisma.user.upsert({
      where: { id: "user_2" },
      update: {},
      create: {
        id: "user_2",
        email: "<EMAIL>",
        name: "Jane Smith",
        isKYCVerified: true,
      },
    }),
    prisma.user.upsert({
      where: { id: "user_3" },
      update: {},
      create: {
        id: "user_3",
        email: "<EMAIL>",
        name: "Alice Johnson",
        isKYCVerified: false,
      },
    }),
  ]);

  console.log(\`✅ Created \${users.length} users\`);

  // Create test projects with full seed data...
  const projects = await Promise.all([
    prisma.project.upsert({
      where: { id: "project_1" },
      update: {},
      create: {
        id: "project_1",
        title: "Neural Finance",
        description: "AI-powered decentralized lending protocol with machine learning risk assessment and dynamic interest rates.",
        imageUrl: "https://via.placeholder.com/400x200/6366f1/ffffff?text=Neural+Finance",
        websiteUrl: "https://neuralfinance.ai",
        deckUrl: "https://example.com/neural-deck.pdf",
        whitepaperUrl: "https://example.com/neural-whitepaper.pdf",
        socialUrls: [
          "https://twitter.com/neuralfinance",
          "https://discord.gg/neuralfinance",
          "https://t.me/neuralfinance"
        ],
        challengeIntro: "Neural Finance revolutionizes DeFi lending through AI-driven risk models. Key challenges: Real-time credit scoring, oracle reliability, regulatory compliance for AI decisions. Market opportunity: $180B+ global lending market with DeFi representing <5% penetration.",
        isApprovedForVoting: true,
      },
    }),
    // Add other projects...
  ]);

  console.log(\`✅ Created \${projects.length} projects\`);
  console.log("🎉 Seeding completed!");
}

seed()
  .catch((e) => {
    console.error("❌ Seeding failed:");
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });`;
  
  fs.writeFileSync(seedPath, seedContent);
  console.log('✅ Restored seed script');
}

function removeSupabaseFiles() {
  console.log('🗑️  Removing Supabase files...');
  
  const supabaseFiles = [
    path.join(serverPath, 'src/lib/supabase.ts'),
    path.join(serverPath, 'supabase'),
    path.join(serverPath, '.env.local')
  ];
  
  supabaseFiles.forEach(filePath => {
    if (fs.existsSync(filePath)) {
      if (fs.lstatSync(filePath).isDirectory()) {
        fs.rmSync(filePath, { recursive: true, force: true });
      } else {
        fs.unlinkSync(filePath);
      }
      console.log(`✅ Removed: ${path.relative(projectRoot, filePath)}`);
    }
  });
}

function updateRootPackageJson() {
  const packageJsonPath = path.join(projectRoot, 'package.json');
  
  if (fs.existsSync(packageJsonPath)) {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    // Restore original scripts
    packageJson.scripts = {
      ...packageJson.scripts,
      'db:push': 'turbo -F server db:push',
      'db:studio': 'turbo -F server db:studio',
      'db:generate': 'turbo -F server db:generate',
      'db:migrate': 'turbo -F server db:migrate'
    };
    
    // Remove Supabase scripts
    delete packageJson.scripts['supabase:start'];
    delete packageJson.scripts['supabase:stop'];
    delete packageJson.scripts['supabase:status'];
    
    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
    console.log('✅ Restored root package.json');
  }
}

async function rollback() {
  console.log('🔄 Starting rollback to Prisma/PostgreSQL setup...');
  
  try {
    const rollbackStart = new Date().toISOString();
    
    // 1. Remove Supabase files
    removeSupabaseFiles();
    
    // 2. Restore Prisma files
    createPrismaFiles();
    restorePrismaContext();
    restoreSeedScript();
    
    // 3. Restore package configurations
    restorePackageJson();
    updateRootPackageJson();
    restoreEnvExample();
    
    // 4. Reinstall Prisma dependencies
    console.log('📦 Reinstalling Prisma dependencies...');
    runCommand('npm uninstall @supabase/supabase-js', serverPath, true);
    
    if (!runCommand('npm install @prisma/client@^6.9.0', serverPath)) {
      console.warn('⚠️  Failed to install @prisma/client - please run manually');
    }
    
    if (!runCommand('npm install -D prisma@^6.9.0', serverPath)) {
      console.warn('⚠️  Failed to install prisma dev dependency - please run manually');
    }
    
    // 5. Generate Prisma client
    console.log('🔧 Generating Prisma client...');
    if (!runCommand('npx prisma generate --schema ./prisma/schema', serverPath)) {
      console.warn('⚠️  Failed to generate Prisma client - please run manually');
    }
    
    // 6. Create rollback log
    const rollbackLog = {
      rollbackDate: rollbackStart,
      rollbackCompleted: new Date().toISOString(),
      steps: [
        'Removed Supabase files',
        'Restored Prisma schema',
        'Restored Prisma context',
        'Restored package configurations',
        'Reinstalled Prisma dependencies',
        'Generated Prisma client'
      ],
      nextSteps: [
        'Update your .env file with PostgreSQL DATABASE_URL',
        'Run: pnpm db:push to sync database schema',
        'Run: pnpm db:seed to restore data',
        'Test the application functionality'
      ]
    };
    
    fs.writeFileSync(
      path.join(logsDir, `rollback-${Date.now()}.json`),
      JSON.stringify(rollbackLog, null, 2)
    );
    
    console.log('✅ Rollback completed successfully!');
    console.log('📝 Next steps:');
    console.log('   1. Update your .env file with PostgreSQL DATABASE_URL');
    console.log('   2. Run: pnpm db:push');
    console.log('   3. Run: pnpm db:seed');
    console.log('   4. Test application functionality');
    console.log('');
    console.log('💡 If you have exported data, you can restore it manually');
    console.log('   using the exported JSON files in migration/data/');
    
  } catch (error) {
    console.error('❌ Rollback failed:', error);
    
    // Log error
    fs.writeFileSync(
      path.join(logsDir, `rollback-error-${Date.now()}.log`),
      `Rollback failed at ${new Date().toISOString()}\n` +
      `Error: ${error.message}\n` +
      `Stack: ${error.stack}\n`
    );
    
    console.log('💡 Manual rollback steps:');
    console.log('   1. Restore Prisma dependencies in apps/server/package.json');
    console.log('   2. Recreate prisma/schema/schema.prisma');
    console.log('   3. Update src/lib/context.ts to use PrismaClient');
    console.log('   4. Remove src/lib/supabase.ts');
    console.log('   5. Run: npm install @prisma/client prisma');
    console.log('   6. Run: npx prisma generate');
    
    process.exit(1);
  }
}

rollback();