#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { createClient } from '@supabase/supabase-js';
import { createHash } from 'crypto';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Environment variables
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
  console.error('❌ Missing required environment variables:');
  console.error('   SUPABASE_URL');
  console.error('   SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

const logsDir = path.join(__dirname, '../logs');
const exportDir = path.join(__dirname, '../data');

// Function to convert CUID to deterministic UUID
function cuidToUuid(cuid) {
  // Create a hash of the CUID and format as UUID v4
  const hash = createHash('sha256').update(cuid).digest('hex');
  const uuid = [
    hash.substr(0, 8),
    hash.substr(8, 4),
    '4' + hash.substr(13, 3), // Version 4
    ((parseInt(hash.substr(16, 1), 16) & 0x3) | 0x8).toString(16) + hash.substr(17, 3), // Variant bits
    hash.substr(20, 12)
  ].join('-');
  return uuid;
}

// Function to convert Prisma column names to Supabase (snake_case)
function convertColumnNames(obj) {
  const converted = {};
  for (const [key, value] of Object.entries(obj)) {
    let newKey = key;
    // Convert camelCase to snake_case
    newKey = newKey.replace(/([A-Z])/g, '_$1').toLowerCase();
    
    // Handle specific mappings
    const mappings = {
      'is_k_y_c_verified': 'is_kyc_verified',
      'created_at': 'created_at',
      'updated_at': 'updated_at',
      'image_u_r_l': 'image_url',
      'website_u_r_l': 'website_url',
      'deck_u_r_l': 'deck_url',
      'whitepaper_u_r_l': 'whitepaper_url',
      'social_u_r_ls': 'social_urls',
      'user_id': 'user_id',
      'project_id': 'project_id',
      'review_id': 'review_id',
      'question_index': 'question_index',
      'a_i_confidence': 'ai_confidence'
    };
    
    if (mappings[newKey]) {
      newKey = mappings[newKey];
    }
    
    converted[newKey] = value;
  }
  return converted;
}

async function importData() {
  console.log('🚀 Starting data import to Supabase...');
  
  try {
    // Check if export data exists
    const exportSummaryPath = path.join(exportDir, 'export-summary.json');
    if (!fs.existsSync(exportSummaryPath)) {
      throw new Error('Export data not found. Please run export script first.');
    }

    const exportSummary = JSON.parse(fs.readFileSync(exportSummaryPath, 'utf8'));
    console.log('📊 Import Summary from Export:');
    console.log(`   Users: ${exportSummary.tables.users}`);
    console.log(`   Projects: ${exportSummary.tables.projects}`);
    console.log(`   Reviews: ${exportSummary.tables.reviews}`);
    console.log(`   Review Responses: ${exportSummary.tables.reviewResponses}`);
    console.log(`   AI Analyses: ${exportSummary.tables.aiAnalyses}`);

    // Create ID mapping for conversion tracking
    const idMappings = {
      users: new Map(),
      projects: new Map(),
      reviews: new Map(),
      reviewResponses: new Map(),
      aiAnalyses: new Map()
    };

    // Import Users
    if (exportSummary.tables.users > 0) {
      console.log('📤 Importing users...');
      const users = JSON.parse(fs.readFileSync(path.join(exportDir, 'users.json'), 'utf8'));
      
      const convertedUsers = users.map(user => {
        const newId = cuidToUuid(user.id);
        idMappings.users.set(user.id, newId);
        
        const converted = convertColumnNames(user);
        converted.id = newId;
        return converted;
      });

      const { error: usersError } = await supabase
        .from('users')
        .insert(convertedUsers);

      if (usersError) {
        throw new Error(`Failed to insert users: ${usersError.message}`);
      }
      console.log(`✅ Imported ${convertedUsers.length} users`);
    }

    // Import Projects
    if (exportSummary.tables.projects > 0) {
      console.log('📤 Importing projects...');
      const projects = JSON.parse(fs.readFileSync(path.join(exportDir, 'projects.json'), 'utf8'));
      
      const convertedProjects = projects.map(project => {
        const newId = cuidToUuid(project.id);
        idMappings.projects.set(project.id, newId);
        
        const converted = convertColumnNames(project);
        converted.id = newId;
        return converted;
      });

      const { error: projectsError } = await supabase
        .from('projects')
        .insert(convertedProjects);

      if (projectsError) {
        throw new Error(`Failed to insert projects: ${projectsError.message}`);
      }
      console.log(`✅ Imported ${convertedProjects.length} projects`);
    }

    // Import Reviews
    if (exportSummary.tables.reviews > 0) {
      console.log('📤 Importing reviews...');
      const reviews = JSON.parse(fs.readFileSync(path.join(exportDir, 'reviews.json'), 'utf8'));
      
      const convertedReviews = reviews.map(review => {
        const newId = cuidToUuid(review.id);
        idMappings.reviews.set(review.id, newId);
        
        const converted = convertColumnNames(review);
        converted.id = newId;
        converted.user_id = idMappings.users.get(review.userId);
        converted.project_id = idMappings.projects.get(review.projectId);
        
        if (!converted.user_id || !converted.project_id) {
          throw new Error(`Missing mapping for review ${review.id}`);
        }
        
        return converted;
      });

      const { error: reviewsError } = await supabase
        .from('reviews')
        .insert(convertedReviews);

      if (reviewsError) {
        throw new Error(`Failed to insert reviews: ${reviewsError.message}`);
      }
      console.log(`✅ Imported ${convertedReviews.length} reviews`);
    }

    // Import Review Responses
    if (exportSummary.tables.reviewResponses > 0) {
      console.log('📤 Importing review responses...');
      const reviewResponses = JSON.parse(fs.readFileSync(path.join(exportDir, 'review_responses.json'), 'utf8'));
      
      const convertedResponses = reviewResponses.map(response => {
        const newId = cuidToUuid(response.id);
        idMappings.reviewResponses.set(response.id, newId);
        
        const converted = convertColumnNames(response);
        converted.id = newId;
        converted.review_id = idMappings.reviews.get(response.reviewId);
        
        if (!converted.review_id) {
          throw new Error(`Missing review mapping for response ${response.id}`);
        }
        
        return converted;
      });

      // Insert in batches to avoid payload size issues
      const batchSize = 100;
      for (let i = 0; i < convertedResponses.length; i += batchSize) {
        const batch = convertedResponses.slice(i, i + batchSize);
        const { error: responsesError } = await supabase
          .from('review_responses')
          .insert(batch);

        if (responsesError) {
          throw new Error(`Failed to insert review responses batch ${i}: ${responsesError.message}`);
        }
      }
      console.log(`✅ Imported ${convertedResponses.length} review responses`);
    }

    // Import AI Analyses
    if (exportSummary.tables.aiAnalyses > 0) {
      console.log('📤 Importing AI analyses...');
      const aiAnalyses = JSON.parse(fs.readFileSync(path.join(exportDir, 'ai_analyses.json'), 'utf8'));
      
      const convertedAnalyses = aiAnalyses.map(analysis => {
        const newId = cuidToUuid(analysis.id);
        idMappings.aiAnalyses.set(analysis.id, newId);
        
        const converted = convertColumnNames(analysis);
        converted.id = newId;
        converted.project_id = idMappings.projects.get(analysis.projectId);
        
        if (!converted.project_id) {
          throw new Error(`Missing project mapping for AI analysis ${analysis.id}`);
        }
        
        return converted;
      });

      const { error: analysesError } = await supabase
        .from('ai_analyses')
        .insert(convertedAnalyses);

      if (analysesError) {
        throw new Error(`Failed to insert AI analyses: ${analysesError.message}`);
      }
      console.log(`✅ Imported ${convertedAnalyses.length} AI analyses`);
    }

    // Save ID mappings for reference
    fs.writeFileSync(
      path.join(exportDir, 'id-mappings.json'),
      JSON.stringify({
        users: Object.fromEntries(idMappings.users),
        projects: Object.fromEntries(idMappings.projects),
        reviews: Object.fromEntries(idMappings.reviews),
        reviewResponses: Object.fromEntries(idMappings.reviewResponses),
        aiAnalyses: Object.fromEntries(idMappings.aiAnalyses)
      }, null, 2)
    );

    // Create import summary
    const importSummary = {
      importDate: new Date().toISOString(),
      originalExportDate: exportSummary.exportDate,
      tablesImported: {
        users: exportSummary.tables.users,
        projects: exportSummary.tables.projects,
        reviews: exportSummary.tables.reviews,
        reviewResponses: exportSummary.tables.reviewResponses,
        aiAnalyses: exportSummary.tables.aiAnalyses
      },
      totalRecordsImported: exportSummary.totalRecords,
      idConversions: {
        users: idMappings.users.size,
        projects: idMappings.projects.size,
        reviews: idMappings.reviews.size,
        reviewResponses: idMappings.reviewResponses.size,
        aiAnalyses: idMappings.aiAnalyses.size
      }
    };

    fs.writeFileSync(
      path.join(exportDir, 'import-summary.json'),
      JSON.stringify(importSummary, null, 2)
    );

    // Log import details
    fs.writeFileSync(
      path.join(logsDir, `import-${Date.now()}.log`),
      `Import completed at ${new Date().toISOString()}\n` +
      `Total records imported: ${importSummary.totalRecordsImported}\n` +
      `Original export date: ${exportSummary.exportDate}\n` +
      `ID mappings saved to: ${path.join(exportDir, 'id-mappings.json')}\n`
    );

    console.log('✅ Data import completed successfully!');
    console.log(`📊 Total records imported: ${importSummary.totalRecordsImported}`);
    console.log(`🔄 ID mappings saved to: ${path.join(exportDir, 'id-mappings.json')}`);

  } catch (error) {
    console.error('❌ Import failed:', error);
    
    // Log error
    fs.writeFileSync(
      path.join(logsDir, `import-error-${Date.now()}.log`),
      `Import failed at ${new Date().toISOString()}\n` +
      `Error: ${error.message}\n` +
      `Stack: ${error.stack}\n`
    );
    
    process.exit(1);
  }
}

importData();