#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { PrismaClient } from '../../apps/server/prisma/generated/index.js';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const prisma = new PrismaClient();

// Ensure logs directory exists
const logsDir = path.join(__dirname, '../logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

const exportDir = path.join(__dirname, '../data');
if (!fs.existsSync(exportDir)) {
  fs.mkdirSync(exportDir, { recursive: true });
}

async function exportData() {
  console.log('🚀 Starting data export from Prisma database...');
  
  try {
    // Export Users
    console.log('📤 Exporting users...');
    const users = await prisma.user.findMany({
      orderBy: { createdAt: 'asc' }
    });
    fs.writeFileSync(
      path.join(exportDir, 'users.json'),
      JSON.stringify(users, null, 2)
    );
    console.log(`✅ Exported ${users.length} users`);

    // Export Projects
    console.log('📤 Exporting projects...');
    const projects = await prisma.project.findMany({
      orderBy: { createdAt: 'asc' }
    });
    fs.writeFileSync(
      path.join(exportDir, 'projects.json'),
      JSON.stringify(projects, null, 2)
    );
    console.log(`✅ Exported ${projects.length} projects`);

    // Export Reviews
    console.log('📤 Exporting reviews...');
    const reviews = await prisma.review.findMany({
      orderBy: { createdAt: 'asc' }
    });
    fs.writeFileSync(
      path.join(exportDir, 'reviews.json'),
      JSON.stringify(reviews, null, 2)
    );
    console.log(`✅ Exported ${reviews.length} reviews`);

    // Export Review Responses
    console.log('📤 Exporting review responses...');
    const reviewResponses = await prisma.reviewResponse.findMany({
      orderBy: { id: 'asc' }
    });
    fs.writeFileSync(
      path.join(exportDir, 'review_responses.json'),
      JSON.stringify(reviewResponses, null, 2)
    );
    console.log(`✅ Exported ${reviewResponses.length} review responses`);

    // Export AI Analyses
    console.log('📤 Exporting AI analyses...');
    const aiAnalyses = await prisma.aIAnalysis.findMany({
      orderBy: { createdAt: 'asc' }
    });
    fs.writeFileSync(
      path.join(exportDir, 'ai_analyses.json'),
      JSON.stringify(aiAnalyses, null, 2)
    );
    console.log(`✅ Exported ${aiAnalyses.length} AI analyses`);

    // Create export summary
    const summary = {
      exportDate: new Date().toISOString(),
      tables: {
        users: users.length,
        projects: projects.length,
        reviews: reviews.length,
        reviewResponses: reviewResponses.length,
        aiAnalyses: aiAnalyses.length
      },
      totalRecords: users.length + projects.length + reviews.length + reviewResponses.length + aiAnalyses.length
    };

    fs.writeFileSync(
      path.join(exportDir, 'export-summary.json'),
      JSON.stringify(summary, null, 2)
    );

    console.log('📊 Export Summary:');
    console.log(`   Users: ${summary.tables.users}`);
    console.log(`   Projects: ${summary.tables.projects}`);
    console.log(`   Reviews: ${summary.tables.reviews}`);
    console.log(`   Review Responses: ${summary.tables.reviewResponses}`);
    console.log(`   AI Analyses: ${summary.tables.aiAnalyses}`);
    console.log(`   Total Records: ${summary.totalRecords}`);

    // Log export details
    fs.writeFileSync(
      path.join(logsDir, `export-${Date.now()}.log`),
      `Export completed at ${new Date().toISOString()}\n` +
      `Total records exported: ${summary.totalRecords}\n` +
      `Files created in: ${exportDir}\n`
    );

    console.log('✅ Data export completed successfully!');
    console.log(`📁 Data exported to: ${exportDir}`);

  } catch (error) {
    console.error('❌ Export failed:', error);
    
    // Log error
    fs.writeFileSync(
      path.join(logsDir, `export-error-${Date.now()}.log`),
      `Export failed at ${new Date().toISOString()}\n` +
      `Error: ${error.message}\n` +
      `Stack: ${error.stack}\n`
    );
    
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

exportData();