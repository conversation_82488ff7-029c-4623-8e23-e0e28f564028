#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const projectRoot = path.join(__dirname, '../..');
const serverPath = path.join(projectRoot, 'apps/server');

function updateFile(filePath, updater) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️  File not found: ${filePath}`);
    return;
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  const updatedContent = updater(content);
  
  if (content !== updatedContent) {
    fs.writeFileSync(filePath, updatedContent);
    console.log(`✅ Updated: ${path.relative(projectRoot, filePath)}`);
  } else {
    console.log(`ℹ️  No changes needed: ${path.relative(projectRoot, filePath)}`);
  }
}

function createSupabaseClient() {
  const supabaseLibPath = path.join(serverPath, 'src/lib/supabase.ts');
  
  const supabaseContent = `import { createClient } from '@supabase/supabase-js';

// Environment variables
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl) {
  throw new Error('Missing SUPABASE_URL environment variable');
}

if (!supabaseServiceKey) {
  throw new Error('Missing SUPABASE_SERVICE_ROLE_KEY environment variable');
}

// Create Supabase client with service role key for server-side operations
export const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Database type definitions
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          name: string;
          is_kyc_verified: boolean;
          created_at: string;
        };
        Insert: {
          id?: string;
          email: string;
          name: string;
          is_kyc_verified?: boolean;
          created_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          name?: string;
          is_kyc_verified?: boolean;
          created_at?: string;
        };
      };
      projects: {
        Row: {
          id: string;
          title: string;
          description: string;
          image_url: string | null;
          website_url: string | null;
          deck_url: string | null;
          whitepaper_url: string | null;
          social_urls: any | null;
          challenge_intro: string;
          is_approved_for_voting: boolean;
          created_at: string;
        };
        Insert: {
          id?: string;
          title: string;
          description: string;
          image_url?: string | null;
          website_url?: string | null;
          deck_url?: string | null;
          whitepaper_url?: string | null;
          social_urls?: any | null;
          challenge_intro: string;
          is_approved_for_voting?: boolean;
          created_at?: string;
        };
        Update: {
          id?: string;
          title?: string;
          description?: string;
          image_url?: string | null;
          website_url?: string | null;
          deck_url?: string | null;
          whitepaper_url?: string | null;
          social_urls?: any | null;
          challenge_intro?: string;
          is_approved_for_voting?: boolean;
          created_at?: string;
        };
      };
      reviews: {
        Row: {
          id: string;
          user_id: string;
          project_id: string;
          overall_sentiment: boolean;
          overall_comments: string | null;
          overall_comments_relevant: boolean;
          overall_relevance_score: number;
          overall_validation_reason: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          project_id: string;
          overall_sentiment: boolean;
          overall_comments?: string | null;
          overall_comments_relevant?: boolean;
          overall_relevance_score?: number;
          overall_validation_reason?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          project_id?: string;
          overall_sentiment?: boolean;
          overall_comments?: string | null;
          overall_comments_relevant?: boolean;
          overall_relevance_score?: number;
          overall_validation_reason?: string | null;
          created_at?: string;
        };
      };
      review_responses: {
        Row: {
          id: string;
          review_id: string;
          dimension: 'PROJECT_FUNDAMENTALS' | 'TEAM_GOVERNANCE' | 'TRANSPARENCY_DOCUMENTATION' | 'TECHNOLOGY_EXECUTION' | 'COMMUNITY_COMMUNICATION' | 'TOKEN_UTILITY_TOKENOMICS';
          question_index: number;
          vote: boolean;
          feedback: string | null;
          feedback_relevant: boolean;
          relevance_score: number;
          validation_reason: string | null;
          ai_confidence: number;
        };
        Insert: {
          id?: string;
          review_id: string;
          dimension: 'PROJECT_FUNDAMENTALS' | 'TEAM_GOVERNANCE' | 'TRANSPARENCY_DOCUMENTATION' | 'TECHNOLOGY_EXECUTION' | 'COMMUNITY_COMMUNICATION' | 'TOKEN_UTILITY_TOKENOMICS';
          question_index: number;
          vote: boolean;
          feedback?: string | null;
          feedback_relevant?: boolean;
          relevance_score?: number;
          validation_reason?: string | null;
          ai_confidence?: number;
        };
        Update: {
          id?: string;
          review_id?: string;
          dimension?: 'PROJECT_FUNDAMENTALS' | 'TEAM_GOVERNANCE' | 'TRANSPARENCY_DOCUMENTATION' | 'TECHNOLOGY_EXECUTION' | 'COMMUNITY_COMMUNICATION' | 'TOKEN_UTILITY_TOKENOMICS';
          question_index?: number;
          vote?: boolean;
          feedback?: string | null;
          feedback_relevant?: boolean;
          relevance_score?: number;
          validation_reason?: string | null;
          ai_confidence?: number;
        };
      };
      ai_analyses: {
        Row: {
          id: string;
          project_id: string;
          project_fundamentals_score: number;
          team_governance_score: number;
          transparency_doc_score: number;
          technology_execution_score: number;
          community_communication_score: number;
          token_utility_tokenomics_score: number;
          overall_score: number;
          analysis: string | null;
          reasoning: string | null;
          strengths: string[];
          weaknesses: string[];
          recommendations: string[];
          confidence: number;
          analysis_version: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          project_id: string;
          project_fundamentals_score?: number;
          team_governance_score?: number;
          transparency_doc_score?: number;
          technology_execution_score?: number;
          community_communication_score?: number;
          token_utility_tokenomics_score?: number;
          overall_score?: number;
          analysis?: string | null;
          reasoning?: string | null;
          strengths?: string[];
          weaknesses?: string[];
          recommendations?: string[];
          confidence?: number;
          analysis_version?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          project_id?: string;
          project_fundamentals_score?: number;
          team_governance_score?: number;
          transparency_doc_score?: number;
          technology_execution_score?: number;
          community_communication_score?: number;
          token_utility_tokenomics_score?: number;
          overall_score?: number;
          analysis?: string | null;
          reasoning?: string | null;
          strengths?: string[];
          weaknesses?: string[];
          recommendations?: string[];
          confidence?: number;
          analysis_version?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      get_project_review_stats: {
        Args: {
          project_uuid: string;
        };
        Returns: {
          total_reviews: number;
          positive_sentiment: number;
          negative_sentiment: number;
          avg_dimension_scores: any;
        }[];
      };
    };
    Enums: {
      review_dimension: 'PROJECT_FUNDAMENTALS' | 'TEAM_GOVERNANCE' | 'TRANSPARENCY_DOCUMENTATION' | 'TECHNOLOGY_EXECUTION' | 'COMMUNITY_COMMUNICATION' | 'TOKEN_UTILITY_TOKENOMICS';
    };
  };
}

export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row'];
export type TablesInsert<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert'];
export type TablesUpdate<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update'];
`;

  fs.writeFileSync(supabaseLibPath, supabaseContent);
  console.log(`✅ Created: ${path.relative(projectRoot, supabaseLibPath)}`);
}

function updateContext() {
  const contextPath = path.join(serverPath, 'src/lib/context.ts');
  
  updateFile(contextPath, (content) => {
    return `import type { NextRequest } from "next/server";
import { supabase } from "./supabase";

export async function createContext(req: NextRequest) {
  // Simple user simulation for now - in a real app this would be from auth
  const userId = req.headers.get("x-user-id") || "user_1";

  console.log("🔐 tRPC Context created with userId:", userId);

  return {
    supabase,
    userId,
  };
}

export type Context = Awaited<ReturnType<typeof createContext>>;
`;
  });
}

function updateReviewsRouter() {
  const reviewsRouterPath = path.join(serverPath, 'src/routers/reviews.ts');
  
  updateFile(reviewsRouterPath, (content) => {
    return `import { z } from "zod";
import { publicProcedure, router } from "../lib/trpc";
import { ReviewDimension, getDimensionInfo } from "../lib/review-constants";
import { validateFeedbackRelevance, validateOverallComments } from "../lib/ai-validation";

const reviewResponseSchema = z.object({
  dimension: z.nativeEnum(ReviewDimension),
  questionIndex: z.number().min(0),
  vote: z.boolean(),
  feedback: z.string().optional(),
});

export const reviewsRouter = router({
  create: publicProcedure
    .input(z.object({
      projectId: z.string(),
      overallSentiment: z.boolean(),
      overallComments: z.string().optional(),
      responses: z.array(reviewResponseSchema),
    }))
    .mutation(async ({ ctx, input }) => {
      console.log("📝 Creating review for userId:", ctx.userId, "projectId:", input.projectId);

      const isDebugMode = process.env.DEBUG_MODE === "true";
      
      // Check if user already reviewed this project (skip in debug mode)
      if (!isDebugMode) {
        const { data: existingReview } = await ctx.supabase
          .from('reviews')
          .select('id')
          .eq('user_id', ctx.userId)
          .eq('project_id', input.projectId)
          .single();

        if (existingReview) {
          throw new Error("You have already reviewed this project");
        }
      } else {
        console.log("🐛 Debug mode: Allowing multiple reviews from same user");
      }

      // Get project details for AI validation
      const { data: project, error: projectError } = await ctx.supabase
        .from('projects')
        .select('id, title, description')
        .eq('id', input.projectId)
        .single();

      if (projectError || !project) {
        throw new Error("Project not found");
      }

      // Validate overall comments with AI if provided
      let overallValidation = null;
      if (input.overallComments && input.overallComments.trim().length > 0) {
        try {
          overallValidation = await validateOverallComments(
            input.overallComments,
            project.title,
            project.description
          );
        } catch (error) {
          console.error("AI validation failed for overall comments:", error);
        }
      }

      // Validate feedback for each response with AI
      const responsesWithValidation = await Promise.all(
        input.responses.map(async (response) => {
          let validation = null;
          
          if (response.feedback && response.feedback.trim().length > 0) {
            try {
              const dimensionInfo = getDimensionInfo(response.dimension);
              validation = await validateFeedbackRelevance(
                response.feedback,
                dimensionInfo.title,
                dimensionInfo.questions,
                project.title,
                project.description
              );
            } catch (error) {
              console.error(\`AI validation failed for dimension \${response.dimension}:\`, error);
            }
          }

          return {
            ...response,
            validation,
          };
        })
      );

      // Create review in Supabase
      const { data: review, error: reviewError } = await ctx.supabase
        .from('reviews')
        .insert({
          user_id: ctx.userId,
          project_id: input.projectId,
          overall_sentiment: input.overallSentiment,
          overall_comments: input.overallComments,
          overall_comments_relevant: overallValidation?.isRelevant ?? true,
          overall_relevance_score: overallValidation?.relevanceScore ?? 5,
          overall_validation_reason: overallValidation?.reasoning,
        })
        .select()
        .single();

      if (reviewError) {
        throw new Error(\`Failed to create review: \${reviewError.message}\`);
      }

      // Create review responses
      const reviewResponsesData = responsesWithValidation.map(response => ({
        review_id: review.id,
        dimension: response.dimension,
        question_index: response.questionIndex,
        vote: response.vote,
        feedback: response.feedback,
        feedback_relevant: response.validation?.isRelevant ?? true,
        relevance_score: response.validation?.relevanceScore ?? 5,
        validation_reason: response.validation?.reasoning,
        ai_confidence: response.validation?.confidence ?? 0,
      }));

      const { error: responsesError } = await ctx.supabase
        .from('review_responses')
        .insert(reviewResponsesData);

      if (responsesError) {
        throw new Error(\`Failed to create review responses: \${responsesError.message}\`);
      }

      return review;
    }),

  getByProject: publicProcedure
    .input(z.object({ projectId: z.string() }))
    .query(async ({ ctx, input }) => {
      const { data: reviews, error } = await ctx.supabase
        .from('reviews')
        .select(\`
          *,
          review_responses(*),
          users!reviews_user_id_fkey(id, name, is_kyc_verified)
        \`)
        .eq('project_id', input.projectId)
        .order('created_at', { ascending: false });

      if (error) {
        throw new Error(\`Failed to fetch reviews: \${error.message}\`);
      }

      // Transform to match expected structure
      return reviews?.map(review => ({
        ...review,
        responses: review.review_responses,
        user: {
          id: review.users?.id,
          name: review.users?.name,
          isKYCVerified: review.users?.is_kyc_verified,
        },
      })) ?? [];
    }),

  getUserReviewForProject: publicProcedure
    .input(z.object({ projectId: z.string() }))
    .query(async ({ ctx, input }) => {
      const { data: review, error } = await ctx.supabase
        .from('reviews')
        .select(\`
          *,
          review_responses(*)
        \`)
        .eq('user_id', ctx.userId)
        .eq('project_id', input.projectId)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 is "not found"
        throw new Error(\`Failed to fetch user review: \${error.message}\`);
      }

      if (!review) {
        return null;
      }

      return {
        ...review,
        responses: review.review_responses,
      };
    }),
});`;
  });
}

function updateProjectsRouter() {
  const projectsRouterPath = path.join(serverPath, 'src/routers/projects.ts');
  
  updateFile(projectsRouterPath, (content) => {
    // Check if file exists, if not create a basic one
    if (!fs.existsSync(projectsRouterPath)) {
      return `import { z } from "zod";
import { publicProcedure, router } from "../lib/trpc";

export const projectsRouter = router({
  getAll: publicProcedure.query(async ({ ctx }) => {
    const { data: projects, error } = await ctx.supabase
      .from('projects')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(\`Failed to fetch projects: \${error.message}\`);
    }

    return projects ?? [];
  }),

  getById: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const { data: project, error } = await ctx.supabase
        .from('projects')
        .select(\`
          *,
          ai_analyses(*)
        \`)
        .eq('id', input.id)
        .single();

      if (error) {
        throw new Error(\`Failed to fetch project: \${error.message}\`);
      }

      return project;
    }),

  getApproved: publicProcedure.query(async ({ ctx }) => {
    const { data: projects, error } = await ctx.supabase
      .from('projects')
      .select('*')
      .eq('is_approved_for_voting', true)
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(\`Failed to fetch approved projects: \${error.message}\`);
    }

    return projects ?? [];
  }),
});`;
    }

    // If file exists, update it
    return content
      .replace(/ctx\.prisma\.project/g, 'ctx.supabase.from(\'projects\')')
      .replace(/\.findMany\(/g, '.select(\'*\')')
      .replace(/\.findUnique\(/g, '.select(\'*\').single()')
      .replace(/where:\s*{([^}]+)}/g, (match, whereClause) => {
        // Convert Prisma where clause to Supabase
        return whereClause.replace(/(\w+):\s*([^,}]+)/g, '.eq(\'$1\', $2)');
      });
  });
}

function updateUsersRouter() {
  const usersRouterPath = path.join(serverPath, 'src/routers/users.ts');
  
  // Create if doesn't exist
  if (!fs.existsSync(usersRouterPath)) {
    const usersContent = `import { z } from "zod";
import { publicProcedure, router } from "../lib/trpc";

export const usersRouter = router({
  getById: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const { data: user, error } = await ctx.supabase
        .from('users')
        .select('*')
        .eq('id', input.id)
        .single();

      if (error) {
        throw new Error(\`Failed to fetch user: \${error.message}\`);
      }

      return user;
    }),

  getCurrent: publicProcedure.query(async ({ ctx }) => {
    const { data: user, error } = await ctx.supabase
      .from('users')
      .select('*')
      .eq('id', ctx.userId)
      .single();

    if (error && error.code !== 'PGRST116') {
      throw new Error(\`Failed to fetch current user: \${error.message}\`);
    }

    return user;
  }),
});`;

    fs.writeFileSync(usersRouterPath, usersContent);
    console.log(`✅ Created: ${path.relative(projectRoot, usersRouterPath)}`);
  }
}

function updatePackageJson() {
  const packageJsonPath = path.join(serverPath, 'package.json');
  
  updateFile(packageJsonPath, (content) => {
    const packageJson = JSON.parse(content);
    
    // Remove Prisma dependencies
    delete packageJson.dependencies['@prisma/client'];
    delete packageJson.devDependencies['prisma'];
    
    // Add Supabase dependencies
    packageJson.dependencies['@supabase/supabase-js'] = '^2.50.0';
    
    // Update scripts
    packageJson.scripts = {
      ...packageJson.scripts,
      'db:types': 'supabase gen types typescript --local > src/lib/database.types.ts',
      'supabase:start': 'supabase start',
      'supabase:stop': 'supabase stop',
      'supabase:status': 'supabase status',
      'supabase:reset': 'supabase db reset'
    };
    
    // Remove old Prisma scripts
    delete packageJson.scripts['db:push'];
    delete packageJson.scripts['db:studio'];
    delete packageJson.scripts['db:generate'];
    delete packageJson.scripts['db:migrate'];
    
    return JSON.stringify(packageJson, null, 2);
  });
}

function updateEnvExample() {
  const envExamplePath = path.join(serverPath, '.env.example');
  
  updateFile(envExamplePath, (content) => {
    return `CORS_ORIGIN=http://localhost:5174
SUPABASE_URL=http://localhost:54321
SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=
OPENROUTER_API_KEY=`;
  });
}

function updateSeedScript() {
  const seedPath = path.join(serverPath, 'src/lib/seed.ts');
  
  updateFile(seedPath, (content) => {
    return `import "dotenv/config";
import { createClient } from "@supabase/supabase-js";

const supabaseUrl = process.env.SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function seed() {
  console.log("🌱 Seeding Supabase database...");

  // Create test users
  const users = [
    {
      id: "user_1",
      email: "<EMAIL>",
      name: "John Doe",
      is_kyc_verified: true,
    },
    {
      id: "user_2",
      email: "<EMAIL>",
      name: "Jane Smith",
      is_kyc_verified: true,
    },
    {
      id: "user_3",
      email: "<EMAIL>",
      name: "Alice Johnson",
      is_kyc_verified: false,
    },
  ];

  for (const user of users) {
    const { error } = await supabase
      .from('users')
      .upsert(user);
    
    if (error) {
      console.error(\`Failed to upsert user \${user.email}:\`, error);
    }
  }

  console.log(\`✅ Created \${users.length} users\`);

  // Create test projects
  const projects = [
    {
      id: "project_1",
      title: "Neural Finance",
      description: "AI-powered decentralized lending protocol with machine learning risk assessment and dynamic interest rates.",
      image_url: "https://via.placeholder.com/400x200/6366f1/ffffff?text=Neural+Finance",
      website_url: "https://neuralfinance.ai",
      deck_url: "https://example.com/neural-deck.pdf",
      whitepaper_url: "https://example.com/neural-whitepaper.pdf",
      social_urls: [
        "https://twitter.com/neuralfinance",
        "https://discord.gg/neuralfinance",
        "https://t.me/neuralfinance"
      ],
      challenge_intro: "Neural Finance revolutionizes DeFi lending through AI-driven risk models. Key challenges: Real-time credit scoring, oracle reliability, regulatory compliance for AI decisions. Market opportunity: $180B+ global lending market with DeFi representing <5% penetration.",
      is_approved_for_voting: true,
    },
    {
      id: "project_2",
      title: "MetaVerse DAO",
      description: "Decentralized governance platform for virtual world development with cross-metaverse asset management.",
      image_url: "https://via.placeholder.com/400x200/8b5cf6/ffffff?text=MetaVerse+DAO",
      website_url: "https://metaversedao.world",
      deck_url: "https://example.com/metaverse-deck.pdf",
      whitepaper_url: "https://example.com/metaverse-whitepaper.pdf",
      social_urls: [
        "https://twitter.com/metaversedao",
        "https://discord.gg/metaversedao",
        "https://reddit.com/r/metaversedao"
      ],
      challenge_intro: "MetaVerse DAO enables community-driven virtual world creation and governance. Key challenges: Cross-platform interoperability, scalable consensus mechanisms, IP rights management. Target market: $400B+ metaverse economy projected by 2030.",
      is_approved_for_voting: true,
    },
    {
      id: "project_3",
      title: "HealthChain Vault",
      description: "Privacy-preserving healthcare data exchange with zero-knowledge proofs and patient-controlled access.",
      image_url: "https://via.placeholder.com/400x200/10b981/ffffff?text=HealthChain+Vault",
      website_url: "https://healthchainvault.health",
      deck_url: "https://example.com/health-deck.pdf",
      whitepaper_url: "https://example.com/health-whitepaper.pdf",
      social_urls: [
        "https://twitter.com/healthchainvault",
        "https://linkedin.com/company/healthchainvault"
      ],
      challenge_intro: "HealthChain Vault secures medical data while enabling research collaboration. Key challenges: HIPAA compliance, zero-knowledge implementation, provider adoption incentives. Market size: $350B+ healthcare IT market with critical need for interoperability.",
      is_approved_for_voting: false,
    },
    {
      id: "project_4",
      title: "Quantum Bridge",
      description: "Post-quantum cryptography blockchain preparing for the quantum computing era with quantum-resistant consensus.",
      image_url: "https://via.placeholder.com/400x200/f59e0b/ffffff?text=Quantum+Bridge",
      website_url: "https://quantumbridge.network",
      deck_url: "https://example.com/quantum-deck.pdf",
      whitepaper_url: "https://example.com/quantum-whitepaper.pdf",
      social_urls: [
        "https://twitter.com/quantumbridge",
        "https://github.com/quantumbridge",
        "https://medium.com/@quantumbridge"
      ],
      challenge_intro: "Quantum Bridge future-proofs blockchain against quantum threats. Key challenges: Performance overhead of post-quantum crypto, migration from current systems, timeline uncertainty. Critical need: $12T+ crypto market vulnerable to quantum attacks within 10-15 years.",
      is_approved_for_voting: true,
    },
    {
      id: "project_5",
      title: "EcoToken Network",
      description: "Regenerative finance platform tokenizing environmental restoration projects with satellite-verified impact tracking.",
      image_url: "https://via.placeholder.com/400x200/059669/ffffff?text=EcoToken+Network",
      website_url: "https://ecotoken.earth",
      deck_url: "https://example.com/eco-deck.pdf",
      whitepaper_url: "https://example.com/eco-whitepaper.pdf",
      social_urls: [
        "https://twitter.com/ecotokennetwork",
        "https://instagram.com/ecotokennetwork",
        "https://linkedin.com/company/ecotokennetwork"
      ],
      challenge_intro: "EcoToken Network finances global environmental restoration through tokenized impact investing. Key challenges: Satellite data integration, impact measurement standardization, long-term project monitoring. Market opportunity: $23T+ nature-positive investment gap by 2030.",
      is_approved_for_voting: true,
    },
  ];

  for (const project of projects) {
    const { error } = await supabase
      .from('projects')
      .upsert(project);
    
    if (error) {
      console.error(\`Failed to upsert project \${project.title}:\`, error);
    }
  }

  console.log(\`✅ Created \${projects.length} projects\`);
  console.log("🎉 Seeding completed!");
}

seed()
  .catch((e) => {
    console.error("❌ Seeding failed:");
    console.error(e);
    process.exit(1);
  });`;
  });
}

async function updateCode() {
  console.log('🔧 Updating application code for Supabase...');
  
  try {
    // Create Supabase client
    createSupabaseClient();
    
    // Update context
    updateContext();
    
    // Update routers
    updateReviewsRouter();
    updateProjectsRouter();
    updateUsersRouter();
    
    // Update package.json
    updatePackageJson();
    
    // Update environment example
    updateEnvExample();
    
    // Update seed script
    updateSeedScript();
    
    console.log('✅ Code update completed successfully!');
    console.log('📝 Next steps:');
    console.log('   1. Run: cd apps/server && npm install');
    console.log('   2. Update your .env file with Supabase credentials');
    console.log('   3. Test the updated application');
    
  } catch (error) {
    console.error('❌ Code update failed:', error);
    process.exit(1);
  }
}

updateCode();