#!/bin/bash

# IBC-CIE Supabase Migration Script
# This script runs the complete migration from Prisma to Supabase

set -e

echo "🚀 Starting IBC-CIE Migration to Supabase"
echo "========================================"

# Check if required environment variables are set
check_env_vars() {
    echo "🔍 Checking environment variables..."
    
    if [ -z "$DATABASE_URL" ]; then
        echo "❌ DATABASE_URL is not set (required for data export)"
        echo "💡 Set it with: export DATABASE_URL='your-postgres-url'"
        exit 1
    fi
    
    if [ -z "$SUPABASE_URL" ]; then
        echo "❌ SUPABASE_URL is not set (required for data import)"
        echo "💡 Set it with: export SUPABASE_URL='your-supabase-url'"
        exit 1
    fi
    
    if [ -z "$SUPABASE_SERVICE_ROLE_KEY" ]; then
        echo "❌ SUPABASE_SERVICE_ROLE_KEY is not set (required for data import)"
        echo "💡 Set it with: export SUPABASE_SERVICE_ROLE_KEY='your-service-key'"
        exit 1
    fi
    
    echo "✅ Environment variables are set"
}

# Install dependencies
setup_dependencies() {
    echo "📦 Setting up migration dependencies..."
    
    if [ ! -f "package.json" ]; then
        echo "❌ package.json not found in migration directory"
        exit 1
    fi
    
    npm install
    mkdir -p data logs
    
    echo "✅ Dependencies installed"
}

# Run migration steps
run_migration() {
    echo "🔄 Running migration steps..."
    
    # Step 1: Export data
    echo "📤 Step 1: Exporting data from Prisma database..."
    node scripts/1-export-data.js
    
    # Step 2: Import data (schema should already be created)
    echo "📥 Step 2: Importing data to Supabase..."
    node scripts/3-import-data.js
    
    # Step 3: Update code
    echo "🔧 Step 3: Updating application code..."
    node scripts/4-update-code.js
    
    # Step 4: Update dependencies
    echo "📦 Step 4: Updating dependencies..."
    node scripts/5-update-dependencies.js
    
    # Step 5: Validate migration
    echo "✅ Step 5: Validating migration..."
    node scripts/6-validate-migration.js
    
    echo "🎉 Migration completed successfully!"
}

# Show post-migration instructions
show_next_steps() {
    echo ""
    echo "📝 Next Steps:"
    echo "=============="
    echo "1. Update your .env file in apps/server/ with Supabase credentials"
    echo "2. Install new dependencies: cd apps/server && npm install"
    echo "3. Test the application: pnpm dev"
    echo "4. Verify all functionality works correctly"
    echo ""
    echo "🔙 If you need to rollback:"
    echo "   cd migration && node scripts/rollback.js"
    echo ""
    echo "📊 Check logs in migration/logs/ for detailed information"
}

# Main execution
main() {
    # Change to migration directory
    cd "$(dirname "$0")"
    
    check_env_vars
    setup_dependencies
    run_migration
    show_next_steps
}

# Handle interruption
cleanup() {
    echo ""
    echo "⚠️  Migration interrupted!"
    echo "🔙 You can rollback with: node scripts/rollback.js"
    exit 1
}

trap cleanup INT

# Show usage if help requested
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "Usage: ./run-migration.sh"
    echo ""
    echo "Environment variables required:"
    echo "  DATABASE_URL              - Current Prisma database URL"
    echo "  SUPABASE_URL             - Supabase project URL"
    echo "  SUPABASE_SERVICE_ROLE_KEY - Supabase service role key"
    echo ""
    echo "Optional environment variables:"
    echo "  DEBUG_MODE               - Enable debug mode (default: false)"
    echo "  BATCH_SIZE              - Import batch size (default: 100)"
    echo ""
    echo "Examples:"
    echo "  export DATABASE_URL='postgresql://user:pass@localhost:5432/db'"
    echo "  export SUPABASE_URL='https://xyz.supabase.co'"
    echo "  export SUPABASE_SERVICE_ROLE_KEY='eyJ...'"
    echo "  ./run-migration.sh"
    exit 0
fi

# Run main function
main "$@"