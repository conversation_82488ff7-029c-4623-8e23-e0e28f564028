# IBC-CIE Migration to Supabase

This directory contains comprehensive migration scripts to move the IBC-CIE project from Prisma/PostgreSQL to Supabase.

## 📋 Migration Overview

The migration involves:
1. **Data Export**: Export all data from current Prisma database
2. **Schema Conversion**: Convert Prisma schema to Supabase SQL
3. **Data Import**: Import data into Supabase with proper UUID conversion
4. **Code Updates**: Update application code to use Supabase client
5. **Environment Configuration**: Update environment variables and dependencies

## 🚀 Migration Steps

### Step 1: Pre-Migration Setup
```bash
# Install required dependencies
npm install -g supabase
npm install @supabase/supabase-js

# Create Supabase project (if not already done)
supabase init
supabase start
```

### Step 2: Export Current Data
```bash
# Export current database data
node scripts/1-export-data.js
```

### Step 3: Create Supabase Schema
```bash
# Run schema migration
supabase db reset
supabase migration new create_ibc_cie_schema
# Copy content from 2-supabase-schema.sql to the new migration file
supabase db push
```

### Step 4: Import Data
```bash
# Import data with UUID conversion
node scripts/3-import-data.js
```

### Step 5: Update Application Code
```bash
# Update code to use Supabase
node scripts/4-update-code.js
```

### Step 6: Update Dependencies
```bash
# Update package.json files
node scripts/5-update-dependencies.js
```

### Step 7: Validate Migration
```bash
# Run validation tests
node scripts/6-validate-migration.js
```

## 🔄 Rollback Procedures

If migration fails or needs to be rolled back:

```bash
# Rollback to Prisma setup
node scripts/rollback.js
```

## 🔧 Configuration

### Environment Variables
- `SUPABASE_URL`: Your Supabase project URL
- `SUPABASE_ANON_KEY`: Your Supabase anonymous key
- `SUPABASE_SERVICE_ROLE_KEY`: Service role key for admin operations
- `DATABASE_URL`: Original Prisma database URL (for export)

### Files Modified
- `apps/server/package.json`
- `apps/server/src/lib/context.ts`
- `apps/server/src/lib/supabase.ts` (new)
- `apps/server/.env.example`
- All tRPC routers updated for Supabase queries

## 📊 Data Mapping

### ID Conversion
- CUID → UUID using deterministic conversion
- Maintains referential integrity
- Preserves existing relationships

### Schema Changes
- Prisma models → Supabase tables
- Enum types properly handled
- Indexes and constraints maintained
- RLS policies added for security

## ⚠️ Important Notes

1. **Backup First**: Always backup your current database before migration
2. **Test Environment**: Run migration on test environment first
3. **Downtime**: Plan for application downtime during migration
4. **Validation**: Thoroughly test all functionality after migration
5. **Rollback Plan**: Have rollback procedures ready

## 📞 Support

If you encounter issues during migration:
1. Check the validation logs in `migration/logs/`
2. Review the rollback procedures
3. Ensure all environment variables are set correctly
4. Verify Supabase project is properly configured