# IBC-CIE Supabase Migration Guide

## 📋 Overview

This guide provides step-by-step instructions for migrating the IBC-CIE project from Prisma/PostgreSQL to Supabase. The migration includes data preservation, schema conversion, and code updates.

## ⚠️ Pre-Migration Requirements

### 1. Environment Setup
```bash
# Install Supabase CLI
npm install -g supabase

# Install Node.js dependencies
npm install @supabase/supabase-js
```

### 2. Backup Current Database
```bash
# Create a full backup of your current PostgreSQL database
pg_dump $DATABASE_URL > backup-$(date +%Y%m%d-%H%M%S).sql
```

### 3. Create Supabase Project
1. Go to [supabase.com](https://supabase.com)
2. Create a new project
3. Note your project URL and API keys

## 🚀 Migration Steps

### Step 1: Export Current Data
```bash
# Navigate to migration directory
cd migration

# Set up environment variables for current database
export DATABASE_URL="your-current-postgres-url"

# Run export script
node scripts/1-export-data.js
```

**Expected Output:**
- Data exported to `migration/data/` directory
- Export summary showing record counts
- Log files in `migration/logs/`

### Step 2: Set Up Supabase Project
```bash
# Initialize Supabase in server directory
cd apps/server
supabase init

# Start local Supabase (for testing)
supabase start

# Or connect to your cloud project
supabase link --project-ref your-project-ref
```

### Step 3: Create Database Schema
```bash
# Create new migration file
supabase migration new create_ibc_cie_schema

# Copy schema content
cp ../../migration/sql/2-supabase-schema.sql supabase/migrations/$(date +%Y%m%d%H%M%S)_create_ibc_cie_schema.sql

# Apply migration
supabase db push
```

### Step 4: Import Data
```bash
# Set Supabase environment variables
export SUPABASE_URL="your-supabase-url"
export SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"

# Navigate back to migration directory
cd ../../migration

# Run import script
node scripts/3-import-data.js
```

### Step 5: Update Application Code
```bash
# Update codebase for Supabase
node scripts/4-update-code.js
```

**Changes Made:**
- Creates new `src/lib/supabase.ts` client
- Updates `src/lib/context.ts` to use Supabase
- Converts all tRPC routers to Supabase queries
- Updates type definitions

### Step 6: Update Dependencies
```bash
# Update package.json and install dependencies
node scripts/5-update-dependencies.js

# Install new dependencies
cd apps/server
npm install
```

### Step 7: Update Environment Variables
```bash
# Copy example environment file
cp .env.example .env

# Update with your Supabase credentials
nano .env
```

Required variables:
```env
SUPABASE_URL=your-supabase-project-url
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
CORS_ORIGIN=http://localhost:5174
OPENROUTER_API_KEY=your-openrouter-key
```

### Step 8: Validate Migration
```bash
# Run validation script
cd ../../migration
node scripts/6-validate-migration.js
```

**Validation Checks:**
- Table structure verification
- Data count comparison
- Referential integrity
- Enum type validation

### Step 9: Test Application
```bash
# Start the application
cd ../apps/server
npm run dev

# In another terminal, start web app
cd ../web
npm run dev
```

**Test Checklist:**
- [ ] Projects load correctly
- [ ] Reviews can be created
- [ ] User authentication works
- [ ] AI validation functions
- [ ] All CRUD operations work

## 🔄 Rollback Procedure

If migration fails or issues are discovered:

```bash
# Stop Supabase services
supabase stop

# Run rollback script
cd migration
node scripts/rollback.js

# Restore original database
psql $DATABASE_URL < your-backup-file.sql

# Reinstall Prisma dependencies
cd apps/server
npm install
npx prisma generate
npx prisma db push
```

## 📊 Data Mapping Reference

### ID Conversion
- **CUID → UUID**: Deterministic conversion using SHA-256 hash
- **Maintains relationships**: All foreign key references preserved
- **Mapping stored**: ID conversions saved in `migration/data/id-mappings.json`

### Schema Conversion
| Prisma | Supabase |
|--------|----------|
| `cuid()` | `uuid_generate_v4()` |
| `String` | `text` |
| `Boolean` | `boolean` |
| `Int` | `integer` |
| `DateTime` | `timestamp with time zone` |
| `Json` | `jsonb` |
| `String[]` | `text[]` |
| `isKYCVerified` | `is_kyc_verified` |
| `createdAt` | `created_at` |

### Table Names
| Prisma Model | Supabase Table |
|--------------|----------------|
| `User` | `users` |
| `Project` | `projects` |
| `Review` | `reviews` |
| `ReviewResponse` | `review_responses` |
| `AIAnalysis` | `ai_analyses` |

## 🔒 Security Considerations

### Row Level Security (RLS)
The migration automatically sets up RLS policies:

- **Public read access** for projects, reviews, and responses
- **User-specific write access** for reviews
- **Admin-only access** for project management
- **Authenticated user access** for AI analyses

### Environment Security
- Use service role key only for server-side operations
- Store anon key for client-side operations
- Never commit API keys to version control
- Use environment variable substitution in production

## 🐛 Troubleshooting

### Common Issues

#### 1. Import Fails with "Invalid UUID"
```bash
# Check ID mappings
cat migration/data/id-mappings.json

# Verify CUID conversion
node -e "
const crypto = require('crypto');
const cuid = 'your-cuid-here';
const hash = crypto.createHash('sha256').update(cuid).digest('hex');
console.log(hash.substr(0, 8) + '-' + hash.substr(8, 4) + '-4' + hash.substr(13, 3) + '-' + ((parseInt(hash.substr(16, 1), 16) & 0x3) | 0x8).toString(16) + hash.substr(17, 3) + '-' + hash.substr(20, 12));
"
```

#### 2. RLS Policies Blocking Access
```sql
-- Temporarily disable RLS for debugging
ALTER TABLE public.users DISABLE ROW LEVEL SECURITY;

-- Re-enable after fixing
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
```

#### 3. Type Errors After Migration
```bash
# Regenerate types
supabase gen types typescript --local > src/lib/database.types.ts

# Update imports in code
```

#### 4. Connection Issues
```bash
# Check Supabase status
supabase status

# Restart local instance
supabase stop
supabase start

# Test connection
psql "postgresql://postgres:postgres@localhost:54322/postgres"
```

### Validation Failures

#### Data Count Mismatches
- Check export logs for errors
- Verify database constraints
- Re-run import with error logging

#### Referential Integrity Issues
- Check foreign key mappings
- Verify UUID conversion consistency
- Use validation script detailed output

## 📞 Support

### Log Files
All operations create detailed logs in `migration/logs/`:
- `export-*.log`: Data export details
- `import-*.log`: Data import details  
- `validation-*.json`: Migration validation results
- `rollback-*.json`: Rollback operation details

### Manual Recovery
If scripts fail, refer to the detailed schema in `migration/sql/2-supabase-schema.sql` and manually:
1. Create tables in Supabase
2. Import data using SQL COPY commands
3. Update application code references
4. Test functionality step by step

### Getting Help
1. Check logs in `migration/logs/` directory
2. Review validation output for specific errors
3. Test individual components after migration
4. Use rollback procedure if needed

## ✅ Post-Migration Checklist

- [ ] All data successfully imported
- [ ] Validation script passes
- [ ] Application starts without errors
- [ ] User authentication works
- [ ] CRUD operations function correctly
- [ ] AI validation still works
- [ ] Performance is acceptable
- [ ] RLS policies are properly configured
- [ ] Environment variables are secure
- [ ] Backup of original database retained
- [ ] Team trained on Supabase operations
- [ ] Documentation updated
- [ ] Monitoring and alerts configured

## 🎯 Next Steps After Migration

1. **Performance Optimization**
   - Review query performance
   - Add additional indexes if needed
   - Configure connection pooling

2. **Security Hardening**
   - Review RLS policies
   - Set up API rate limiting
   - Configure audit logging

3. **Monitoring Setup**
   - Enable Supabase monitoring
   - Set up alerts for errors
   - Monitor database performance

4. **Team Training**
   - Train team on Supabase dashboard
   - Update deployment procedures
   - Document new workflows