-- IBC-CIE Supabase Schema Migration
-- This file creates the complete database schema for Supabase

-- Enable necessary extensions
create extension if not exists "uuid-ossp";

-- Create custom types
create type review_dimension as enum (
  'PROJECT_FUNDAMENTALS',
  'TEAM_GOVERNANCE',
  'TRANSPARENCY_DOCUMENTATION',
  'TECHNOLOGY_EXECUTION',
  'COMMUNITY_COMMUNICATION',
  'TOKEN_UTILITY_TOKENOMICS'
);

-- Create users table
create table public.users (
  id uuid primary key default uuid_generate_v4(),
  email text unique not null,
  name text not null,
  is_kyc_verified boolean default false,
  created_at timestamp with time zone default now()
);

-- Create projects table
create table public.projects (
  id uuid primary key default uuid_generate_v4(),
  title text not null,
  description text not null,
  image_url text,
  website_url text,
  deck_url text,
  whitepaper_url text,
  social_urls jsonb,
  challenge_intro text not null,
  is_approved_for_voting boolean default false,
  created_at timestamp with time zone default now()
);

-- Create reviews table
create table public.reviews (
  id uuid primary key default uuid_generate_v4(),
  user_id uuid not null references public.users(id) on delete cascade,
  project_id uuid not null references public.projects(id) on delete cascade,
  overall_sentiment boolean not null,
  overall_comments text,
  overall_comments_relevant boolean default true,
  overall_relevance_score integer default 50,
  overall_validation_reason text,
  created_at timestamp with time zone default now(),
  unique(user_id, project_id)
);

-- Create review_responses table
create table public.review_responses (
  id uuid primary key default uuid_generate_v4(),
  review_id uuid not null references public.reviews(id) on delete cascade,
  dimension review_dimension not null,
  question_index integer not null,
  vote boolean not null,
  feedback text,
  feedback_relevant boolean default true,
  relevance_score integer default 50,
  validation_reason text,
  ai_confidence integer default 0,
  unique(review_id, dimension, question_index)
);

-- Create ai_analyses table
create table public.ai_analyses (
  id uuid primary key default uuid_generate_v4(),
  project_id uuid unique not null references public.projects(id) on delete cascade,
  project_fundamentals_score integer default 0,
  team_governance_score integer default 0,
  transparency_doc_score integer default 0,
  technology_execution_score integer default 0,
  community_communication_score integer default 0,
  token_utility_tokenomics_score integer default 0,
  overall_score integer default 0,
  analysis text,
  reasoning text,
  strengths text[],
  weaknesses text[],
  recommendations text[],
  confidence integer default 0,
  analysis_version text default '1.0',
  created_at timestamp with time zone default now(),
  updated_at timestamp with time zone default now()
);

-- Create indexes for better performance
create index idx_users_email on public.users(email);
create index idx_projects_created_at on public.projects(created_at);
create index idx_projects_approved on public.projects(is_approved_for_voting);
create index idx_reviews_project_id on public.reviews(project_id);
create index idx_reviews_user_id on public.reviews(user_id);
create index idx_reviews_created_at on public.reviews(created_at);
create index idx_review_responses_review_id on public.review_responses(review_id);
create index idx_review_responses_dimension on public.review_responses(dimension);
create index idx_ai_analyses_project_id on public.ai_analyses(project_id);

-- Create updated_at trigger function
create or replace function update_updated_at_column()
returns trigger as $$
begin
  new.updated_at = now();
  return new;
end;
$$ language plpgsql;

-- Create trigger for ai_analyses updated_at
create trigger update_ai_analyses_updated_at
  before update on public.ai_analyses
  for each row
  execute function update_updated_at_column();

-- Set up Row Level Security (RLS) policies
alter table public.users enable row level security;
alter table public.projects enable row level security;
alter table public.reviews enable row level security;
alter table public.review_responses enable row level security;
alter table public.ai_analyses enable row level security;

-- Create RLS policies
-- Users can read all users (for public profiles)
create policy "Users can view all users" on public.users
  for select using (true);

-- Users can update their own profile
create policy "Users can update own profile" on public.users
  for update using (auth.uid()::text = id::text);

-- Projects are publicly readable
create policy "Projects are publicly readable" on public.projects
  for select using (true);

-- Only authenticated users can create projects (admin functionality)
create policy "Authenticated users can create projects" on public.projects
  for insert with check (auth.role() = 'authenticated');

-- Only authenticated users can update projects (admin functionality)
create policy "Authenticated users can update projects" on public.projects
  for update using (auth.role() = 'authenticated');

-- Reviews are publicly readable
create policy "Reviews are publicly readable" on public.reviews
  for select using (true);

-- Users can create reviews
create policy "Users can create reviews" on public.reviews
  for insert with check (auth.uid()::text = user_id::text);

-- Users can update their own reviews
create policy "Users can update own reviews" on public.reviews
  for update using (auth.uid()::text = user_id::text);

-- Review responses are publicly readable
create policy "Review responses are publicly readable" on public.review_responses
  for select using (true);

-- Users can create review responses for their reviews
create policy "Users can create review responses" on public.review_responses
  for insert with check (
    exists (
      select 1 from public.reviews r 
      where r.id = review_id and r.user_id::text = auth.uid()::text
    )
  );

-- AI analyses are publicly readable
create policy "AI analyses are publicly readable" on public.ai_analyses
  for select using (true);

-- Only authenticated users can manage AI analyses (admin functionality)
create policy "Authenticated users can create AI analyses" on public.ai_analyses
  for insert with check (auth.role() = 'authenticated');

create policy "Authenticated users can update AI analyses" on public.ai_analyses
  for update using (auth.role() = 'authenticated');

-- Grant necessary permissions
grant usage on schema public to anon, authenticated;
grant all on all tables in schema public to anon, authenticated;
grant all on all sequences in schema public to anon, authenticated;

-- Comments for documentation
comment on table public.users is 'User accounts and profiles';
comment on table public.projects is 'Blockchain projects to be reviewed';
comment on table public.reviews is 'User reviews of projects';
comment on table public.review_responses is 'Individual dimension responses within reviews';
comment on table public.ai_analyses is 'AI-generated project analyses and scores';

comment on column public.users.is_kyc_verified is 'Whether user has completed KYC verification';
comment on column public.projects.is_approved_for_voting is 'Whether project is approved for community voting';
comment on column public.reviews.overall_sentiment is 'Overall positive/negative sentiment';
comment on column public.review_responses.vote is 'Thumbs up/down vote for specific question';
comment on column public.ai_analyses.confidence is 'AI confidence score (0-100)';

-- Create helper functions
create or replace function get_project_review_stats(project_uuid uuid)
returns table(
  total_reviews bigint,
  positive_sentiment bigint,
  negative_sentiment bigint,
  avg_dimension_scores jsonb
) language plpgsql as $$
declare
  dimension_stats jsonb;
begin
  -- Get basic review stats
  select 
    count(*),
    count(*) filter (where overall_sentiment = true),
    count(*) filter (where overall_sentiment = false)
  into total_reviews, positive_sentiment, negative_sentiment
  from public.reviews
  where project_id = project_uuid;

  -- Get average scores by dimension
  select jsonb_object_agg(
    dimension,
    round(avg(case when vote then 100 else 0 end), 2)
  ) into dimension_stats
  from public.review_responses rr
  join public.reviews r on r.id = rr.review_id
  where r.project_id = project_uuid
  group by dimension;

  avg_dimension_scores := coalesce(dimension_stats, '{}'::jsonb);
  
  return next;
end;
$$;

comment on function get_project_review_stats is 'Get aggregated review statistics for a project';