{"name": "ibc-cie-migration", "version": "1.0.0", "description": "Migration scripts for IBC-CIE from Prisma to Supabase", "type": "module", "scripts": {"migrate": "npm run export && npm run import && npm run update-code && npm run validate", "export": "node scripts/1-export-data.js", "import": "node scripts/3-import-data.js", "update-code": "node scripts/4-update-code.js", "update-deps": "node scripts/5-update-dependencies.js", "validate": "node scripts/6-validate-migration.js", "rollback": "node scripts/rollback.js", "setup": "mkdir -p data logs && npm install", "clean": "rm -rf data/* logs/*"}, "dependencies": {"@supabase/supabase-js": "^2.50.0"}, "devDependencies": {}, "keywords": ["migration", "supabase", "prisma", "database"], "author": "IBC-CIE Team", "license": "MIT"}