# IBC-CIE Quick Start Guide

Get the IBC-CIE project running locally in minutes with this streamlined setup guide.

## 🚀 One-Command Setup

For first-time setup, run our automated setup script:

```bash
./scripts/setup-local.sh
```

This script will:
- ✅ Check all dependencies
- ✅ Install project dependencies
- ✅ Configure environment files
- ✅ Start Supabase services
- ✅ Apply database migrations
- ✅ Generate TypeScript types
- ✅ Validate the complete setup

## 📋 Prerequisites

Make sure you have these installed:

- **Node.js 18+** - [Download here](https://nodejs.org/)
- **Docker** - [Download here](https://docs.docker.com/get-docker/)
- **pnpm** - Install with `npm install -g pnpm`

## ⚡ Quick Commands

After initial setup, use these commands daily:

```bash
# Start all services
pnpm dev

# Access your application
# Web: http://localhost:5174
# API: http://localhost:3000
# Supabase Studio: http://localhost:54323
```

## 🔧 Essential Commands

```bash
# Database Management
pnpm db:reset          # Reset database with fresh data
pnpm db:studio         # Open Supabase Studio
pnpm db:types          # Generate TypeScript types

# Environment & Health
pnpm validate:env      # Check environment configuration
pnpm health:check      # Run comprehensive health checks

# Supabase Services
pnpm supabase:start    # Start Supabase services
pnpm supabase:stop     # Stop Supabase services
pnpm supabase:status   # Check service status
```

## 🔑 Configuration

### Environment Setup

1. **Copy environment template**:
   ```bash
   cp .env.local.example .env.local
   ```

2. **Add your OpenAI API key** to `.env.local`:
   ```env
   OPENAI_API_KEY=sk-your-actual-openai-key-here
   ```

3. **Validate configuration**:
   ```bash
   pnpm validate:env
   ```

### Service URLs

Once running, access these services:

| Service | URL | Purpose |
|---------|-----|---------|
| Web App | http://localhost:5174 | React frontend |
| API Server | http://localhost:3000 | Next.js API routes |
| Supabase Studio | http://localhost:54323 | Database management |
| Database | postgresql://postgres:postgres@127.0.0.1:54322/postgres | Direct DB access |
| Email Testing | http://localhost:54324 | Inbucket email interface |

## 🩺 Health Checks

Run comprehensive health checks anytime:

```bash
pnpm health:check
```

This checks:
- ✅ Node.js and dependencies
- ✅ Docker status
- ✅ Supabase services
- ✅ Database connectivity
- ✅ Environment configuration
- ✅ Development servers
- ✅ External API connectivity

## 🔄 Reset & Refresh

### Fresh Database Reset

```bash
pnpm reset:db
```

### Complete Environment Reset

```bash
# Stop everything
pnpm supabase:stop

# Clean up Docker
docker system prune -f

# Fresh start
./scripts/setup-local.sh
```

## 🐛 Troubleshooting

### Common Issues

**1. Supabase won't start**
```bash
# Check Docker
docker ps
# Restart Docker and try again
```

**2. Port conflicts**
```bash
# Check what's using ports
lsof -i :54321
lsof -i :3000
lsof -i :5174
```

**3. Environment issues**
```bash
# Validate your setup
pnpm validate:env
```

**4. Database connection errors**
```bash
# Reset database
pnpm reset:db --force
```

### Getting Help

1. **Run health check**: `pnpm health:check`
2. **Check logs**: `supabase logs`
3. **Validate environment**: `pnpm validate:env`
4. **Reset if needed**: `pnpm reset:db`

## 📚 Next Steps

Once everything is running:

1. **Explore the database** at http://localhost:54323
2. **Check the API** at http://localhost:3000/trpc
3. **Start the frontend** at http://localhost:5174
4. **Review the code** in `apps/web/` and `apps/server/`

## 🔗 Useful Links

- [Full Supabase Setup Guide](./SUPABASE_SETUP.md)
- [Project Documentation](./CLAUDE.md)
- [Supabase Docs](https://supabase.com/docs)
- [Next.js Docs](https://nextjs.org/docs)
- [React Router Docs](https://reactrouter.com/)

---

**Need help?** Run `pnpm health:check` for a comprehensive system diagnosis!