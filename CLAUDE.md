# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 🚀 Development Commands

```bash
# Start all applications in development
pnpm dev

# Start specific applications
pnpm dev:web        # Frontend only (React Router + Vite)
pnpm dev:server     # Backend only (Next.js + tRPC)

# Build and type checking
pnpm build          # Build all applications
pnpm check-types    # TypeScript validation across monorepo

# Database operations
npx supabase start     # Start local Supabase stack
npx supabase stop      # Stop local Supabase stack
npx supabase status    # Check Supabase services status
npx supabase db reset  # Reset local database with migrations
npx supabase gen types typescript --local > apps/web/src/types/database.ts  # Generate TypeScript types
```

## 🏗️ Architecture Overview

This is a **Better-T-Stack** monorepo with TypeScript-first full-stack architecture powered by **Supabase**:

### Frontend (`apps/web/`)
- **React Router 7** with file-based routing (`src/routes/`)
- **Vite** as build tool with TailwindCSS 4
- **shadcn/ui** components in `src/components/ui/`
- **Supabase client** for real-time database operations
- **tRPC client** for enhanced type-safe API calls
- **TanStack Query** for state management and caching
- **Supabase Auth** for user authentication

### Backend (`apps/server/`)
- **Next.js 15** with Turbopack for API routes
- **tRPC** server providing type-safe APIs at `/trpc` endpoint
- **Supabase** as primary database with Row Level Security
- **Custom tRPC context** with Supabase integration in `src/lib/context.ts`

### Key Integration Points
- **Supabase Database** with PostgreSQL + Row Level Security
- **Real-time subscriptions** for live updates across the platform
- **Supabase Auth** integrated with custom user profiles
- **tRPC router** defined in `apps/server/src/routers/index.ts`
- **Database types** generated from Supabase to `apps/web/src/types/database.ts`
- **Hybrid approach** using both Supabase direct queries and tRPC for complex operations

## 🔧 Configuration Notes

### Environment Variables
- **Supabase**: `SUPABASE_URL`, `SUPABASE_ANON_KEY`, `SUPABASE_SERVICE_ROLE_KEY`
- **Web app**: `VITE_SUPABASE_URL`, `VITE_SUPABASE_ANON_KEY`
- **Server**: `OPENROUTER_API_KEY` for AI validation
- **Development**: `VITE_SERVER_URL`, `CORS_ORIGIN`
- Environment files: `.env.example` shows all required variables

### Database Setup
- **Supabase** handles database infrastructure automatically
- **Local development** uses Supabase CLI with Docker
- **Migrations** stored in `supabase/migrations/`
- **Row Level Security** policies protect data access
- **Real-time** enabled for live updates

### TypeScript Setup
- Monorepo uses workspace TypeScript configurations
- React Router has built-in type generation (`react-router typegen`)
- **Supabase types** generated automatically from database schema
- **tRPC** provides end-to-end type safety for complex operations

## 🎯 Development Workflow

1. **Supabase Setup**: Install Supabase CLI and run `npx supabase start`
2. **Environment Setup**: Copy `.env.example` and configure Supabase variables
3. **Database Migration**: Run `npx supabase db reset` to apply schema
4. **Type Generation**: Run `npx supabase gen types typescript --local > apps/web/src/types/database.ts`
5. **Development**: Use `pnpm dev` to run both frontend (port 5173) and backend (port 3000)
6. **Type Safety**: Run `pnpm check-types` before commits

## 🔍 Review System Architecture

This is a **community-driven blockchain project evaluation platform** with sophisticated review mechanics:

### Review Components
- **ReviewForm** (`apps/web/src/components/review-form.tsx`): Main review submission interface
- **DimensionReview** (`apps/web/src/components/dimension-review.tsx`): Individual dimension evaluation with thumbs voting
- **ThumbsVote** (`apps/web/src/components/thumbs-vote.tsx`): Up/down voting component
- **Review Constants** (`apps/web/src/lib/review-constants.ts`): 6 dimensions with specific questions each

### AI Integration
- **OpenRouter API** with Gemini 2.0 Flash for feedback validation
- **Structured validation** checking relevance and quality of user feedback
- **Validation scoring** displayed via ValidationBadge component
- **AI validation logic** in `apps/server/src/lib/ai-validation.ts`
- **Real-time AI processing** with Supabase edge functions for scalability

### Review Data Flow
1. **User authentication** via Supabase Auth with KYC verification requirement
2. **User submits** review with dimension scores and feedback
3. **AI validates** feedback relevance using OpenRouter/Gemini
4. **Supabase stores** validated responses with Row Level Security
5. **Real-time updates** broadcast changes to all connected clients
6. **Spider chart** visualizes aggregated community scores with live updates
7. **One review per user per project** constraint enforced via database policies

## 📁 Critical File Locations

### Core Architecture
- **Supabase Config**: `supabase/config.toml`
- **Database Schema**: `supabase/migrations/20250617000001_initial_schema.sql`
- **Supabase Client (Server)**: `apps/server/src/lib/supabase.ts`
- **Supabase Client (Web)**: `apps/web/src/lib/supabase.ts`
- **Database Types**: `apps/web/src/types/database.ts` (generated)
- **Authentication**: `apps/web/src/hooks/useAuth.ts`
- **tRPC Router**: `apps/server/src/routers/index.ts`
- **UI Components**: `apps/web/src/components/ui/`
- **Route Pages**: `apps/web/src/routes/`

### Review System
- **Review Router**: `apps/server/src/routers/reviews.ts`
- **Project Router**: `apps/server/src/routers/projects.ts`
- **Users Router**: `apps/server/src/routers/users.ts`
- **Review Form**: `apps/web/src/components/review-form.tsx`
- **Dimension Questions**: `apps/server/src/lib/review-constants.ts`
- **Spider Chart**: `apps/web/src/components/spider-chart.tsx`
- **AI Validation**: `apps/server/src/lib/ai-validation.ts`

### Authentication System
- **Auth Provider**: `apps/web/src/components/auth-provider.tsx`
- **Auth Hooks**: `apps/web/src/hooks/useAuth.ts`
- **Auth Forms**: `apps/web/src/components/auth-form.tsx`
- **User Menu**: `apps/web/src/components/user-menu.tsx`

### Key Routes
- **Home**: `apps/web/src/routes/_index.tsx` - Project listing and dashboard
- **Project Details**: `apps/web/src/routes/projects.$id.tsx` - Individual project view
- **Submit Review**: `apps/web/src/routes/projects.$id.review.tsx` - Review submission form
- **Review Submission**: `apps/web/src/routes/submit-review.tsx` - Review processing