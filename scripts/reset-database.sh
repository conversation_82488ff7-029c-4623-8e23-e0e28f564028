#!/bin/bash

# IBC-CIE Database Reset Script
# This script resets the local Supabase database with fresh migrations and seed data

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Supabase is running
check_supabase() {
    print_status "Checking Supabase status..."
    
    if ! command -v supabase &> /dev/null; then
        print_error "Supabase CLI is not installed. Please install it first."
        exit 1
    fi
    
    if ! supabase status &> /dev/null; then
        print_warning "Supabase is not running. Starting Supabase..."
        supabase start
        
        # Wait for services to be ready
        sleep 10
    fi
    
    print_success "Supabase is running"
}

# Reset the database
reset_database() {
    print_status "Resetting database..."
    
    # Stop any running processes that might be using the database
    print_status "Stopping development servers..."
    pkill -f "next dev" || true
    pkill -f "react-router dev" || true
    
    # Reset the database
    print_status "Applying database reset..."
    supabase db reset --linked=false
    
    print_success "Database reset completed"
}

# Generate fresh types
generate_types() {
    print_status "Generating fresh TypeScript types..."
    
    # Generate types using local Supabase
    supabase gen types --lang=typescript --local > apps/server/src/types/supabase.ts
    
    print_success "TypeScript types generated"
}

# Validate database
validate_database() {
    print_status "Validating database..."
    
    # Check if we can connect to the database
    if psql "postgresql://postgres:postgres@127.0.0.1:54322/postgres" -c "SELECT 1;" &> /dev/null; then
        print_success "✅ Database connection successful"
    else
        print_error "❌ Cannot connect to database"
        return 1
    fi
    
    # Check if tables exist
    table_count=$(psql "postgresql://postgres:postgres@127.0.0.1:54322/postgres" -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" 2>/dev/null | xargs || echo "0")
    
    if [ "$table_count" -gt 0 ]; then
        print_success "✅ Database tables created ($table_count tables)"
    else
        print_warning "⚠️  No tables found in database"
    fi
    
    # Check if seed data exists
    user_count=$(psql "postgresql://postgres:postgres@127.0.0.1:54322/postgres" -t -c "SELECT COUNT(*) FROM users;" 2>/dev/null | xargs || echo "0")
    
    if [ "$user_count" -gt 0 ]; then
        print_success "✅ Seed data loaded ($user_count users)"
    else
        print_warning "⚠️  No seed data found"
    fi
}

# Show database info
show_database_info() {
    echo ""
    echo "📊 Database Information:"
    echo "   • URL: postgresql://postgres:postgres@127.0.0.1:54322/postgres"
    echo "   • Studio: http://localhost:54323"
    echo "   • API: http://localhost:54321"
    echo ""
    
    # Get table counts
    if command -v psql &> /dev/null; then
        print_status "Table counts:"
        psql "postgresql://postgres:postgres@127.0.0.1:54322/postgres" -c "
            SELECT 
                schemaname,
                tablename,
                n_tup_ins as inserted_rows
            FROM pg_stat_user_tables 
            WHERE schemaname = 'public'
            ORDER BY tablename;
        " 2>/dev/null || print_warning "Could not retrieve table information"
    fi
    
    echo ""
    echo "🚀 Next steps:"
    echo "   • Run 'pnpm dev' to start development servers"
    echo "   • Visit http://localhost:54323 to explore data in Supabase Studio"
    echo "   • Run 'pnpm db:types' to regenerate types if needed"
    echo ""
}

# Main execution
main() {
    echo "🔄 Resetting IBC-CIE database..."
    echo "This will destroy all existing data and recreate the database with fresh migrations and seed data."
    echo ""
    
    # Confirmation prompt
    read -p "Are you sure you want to reset the database? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Database reset cancelled."
        exit 0
    fi
    
    check_supabase
    reset_database
    generate_types
    validate_database
    show_database_info
    
    print_success "Database reset completed successfully! 🎉"
}

# Handle command line arguments
case "${1:-}" in
    --force|-f)
        # Skip confirmation prompt
        check_supabase
        reset_database
        generate_types
        validate_database
        show_database_info
        print_success "Database reset completed successfully! 🎉"
        ;;
    --help|-h)
        echo "Usage: $0 [--force|-f] [--help|-h]"
        echo ""
        echo "Options:"
        echo "  --force, -f    Skip confirmation prompt"
        echo "  --help, -h     Show this help message"
        echo ""
        echo "This script resets the local Supabase database with fresh migrations and seed data."
        ;;
    *)
        main "$@"
        ;;
esac