#!/usr/bin/env node

/**
 * Environment Validation Script for IBC-CIE
 * This script validates that all required environment variables are properly configured
 */

const fs = require('fs');
const path = require('path');

// ANSI color codes
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

// Helper functions for colored output
const log = {
  error: (msg) => console.log(`${colors.red}[ERROR]${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}[SUCCESS]${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}[WARNING]${colors.reset} ${msg}`),
  info: (msg) => console.log(`${colors.blue}[INFO]${colors.reset} ${msg}`),
  title: (msg) => console.log(`${colors.bold}${colors.blue}${msg}${colors.reset}`),
};

// Required environment variables for different environments
const requiredVars = {
  development: {
    server: [
      'SUPABASE_URL',
      'SUPABASE_ANON_KEY', 
      'SUPABASE_SERVICE_ROLE_KEY',
      'OPENAI_API_KEY',
      'NODE_ENV'
    ],
    web: [
      'VITE_SUPABASE_URL',
      'VITE_SUPABASE_ANON_KEY',
      'VITE_SERVER_URL'
    ]
  },
  production: {
    server: [
      'SUPABASE_URL',
      'SUPABASE_ANON_KEY',
      'SUPABASE_SERVICE_ROLE_KEY', 
      'OPENAI_API_KEY',
      'NODE_ENV',
      'NEXT_PUBLIC_APP_URL'
    ],
    web: [
      'VITE_SUPABASE_URL',
      'VITE_SUPABASE_ANON_KEY',
      'VITE_SERVER_URL'
    ]
  },
  test: {
    server: [
      'SUPABASE_URL',
      'SUPABASE_ANON_KEY',
      'SUPABASE_SERVICE_ROLE_KEY',
      'NODE_ENV'
    ],
    web: [
      'VITE_SUPABASE_URL',
      'VITE_SUPABASE_ANON_KEY'
    ]
  }
};

// Optional variables with descriptions
const optionalVars = {
  'CORS_ORIGIN': 'CORS origin for API requests',
  'DATABASE_URL': 'Direct database connection string',
  'AUTH_SECRET': 'Secret for session encryption',
  'PROJECT_REF': 'Supabase project reference for type generation'
};

// Validation patterns
const patterns = {
  'SUPABASE_URL': /^https?:\/\/.*\.supabase\.co$|^http:\/\/127\.0\.0\.1:54321$/,
  'OPENAI_API_KEY': /^sk-[a-zA-Z0-9-_]+$/,
  'NODE_ENV': /^(development|production|test)$/,
  'VITE_SUPABASE_URL': /^https?:\/\/.*\.supabase\.co$|^http:\/\/127\.0\.0\.1:54321$/,
  'VITE_SERVER_URL': /^https?:\/\/.+$/,
  'NEXT_PUBLIC_APP_URL': /^https?:\/\/.+$/
};

// Load environment file
function loadEnvFile(filePath) {
  if (!fs.existsSync(filePath)) {
    return null;
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  const env = {};
  
  content.split('\n').forEach(line => {
    const trimmed = line.trim();
    if (trimmed && !trimmed.startsWith('#') && trimmed.includes('=')) {
      const [key, ...valueParts] = trimmed.split('=');
      const value = valueParts.join('=').trim();
      env[key.trim()] = value;
    }
  });
  
  return env;
}

// Validate environment variables
function validateEnvironment(env, requiredVars, envName) {
  const results = {
    missing: [],
    invalid: [],
    valid: [],
    warnings: []
  };
  
  // Check required variables
  requiredVars.forEach(varName => {
    const value = env[varName];
    
    if (!value || value === 'your-key-here' || value === 'your-openai-key-here') {
      results.missing.push(varName);
    } else if (patterns[varName] && !patterns[varName].test(value)) {
      results.invalid.push({
        name: varName,
        value: value,
        pattern: patterns[varName].toString()
      });
    } else {
      results.valid.push(varName);
    }
  });
  
  // Check for suspicious values
  Object.keys(env).forEach(key => {
    const value = env[key];
    if (value === 'undefined' || value === 'null' || value === '') {
      results.warnings.push(`${key} has suspicious value: "${value}"`);
    }
  });
  
  return results;
}

// Check if Supabase is accessible
async function checkSupabaseConnection(supabaseUrl, anonKey) {
  try {
    const response = await fetch(`${supabaseUrl}/rest/v1/`, {
      headers: {
        'apikey': anonKey,
        'Authorization': `Bearer ${anonKey}`
      }
    });
    
    return response.ok;
  } catch (error) {
    return false;
  }
}

// Check if OpenAI API key is valid
async function checkOpenAIKey(apiKey) {
  if (!apiKey || !apiKey.startsWith('sk-')) {
    return false;
  }
  
  try {
    const response = await fetch('https://api.openai.com/v1/models', {
      headers: {
        'Authorization': `Bearer ${apiKey}`
      }
    });
    
    return response.ok;
  } catch (error) {
    return false;
  }
}

// Main validation function
async function validateSetup() {
  log.title('🔍 IBC-CIE Environment Validation');
  console.log('');
  
  const environment = process.env.NODE_ENV || 'development';
  log.info(`Validating ${environment} environment`);
  console.log('');
  
  // Check for environment files
  const envFiles = [
    { path: '.env.local', required: environment === 'development' },
    { path: '.env.production', required: environment === 'production' },
    { path: '.env.test', required: environment === 'test' },
    { path: 'apps/web/.env.local', required: false },
    { path: 'apps/server/.env.local', required: false }
  ];
  
  let hasValidEnv = false;
  let serverEnv = {};
  let webEnv = {};
  
  // Load environment files
  envFiles.forEach(({ path: envPath, required }) => {
    const fullPath = path.resolve(envPath);
    const env = loadEnvFile(fullPath);
    
    if (env) {
      log.success(`Found ${envPath}`);
      hasValidEnv = true;
      
      // Merge environment variables
      if (envPath.includes('web')) {
        webEnv = { ...webEnv, ...env };
      } else {
        serverEnv = { ...serverEnv, ...env };
      }
    } else if (required) {
      log.error(`Missing required environment file: ${envPath}`);
    } else {
      log.warning(`Optional environment file not found: ${envPath}`);
    }
  });
  
  // Also load from process.env
  serverEnv = { ...serverEnv, ...process.env };
  
  if (!hasValidEnv) {
    log.error('No environment files found. Please run setup script or create .env.local');
    process.exit(1);
  }
  
  console.log('');
  
  // Validate server environment
  log.title('🖥️  Server Environment Validation');
  const serverRequired = requiredVars[environment]?.server || requiredVars.development.server;
  const serverResults = validateEnvironment(serverEnv, serverRequired, 'server');
  
  // Report server results
  if (serverResults.valid.length > 0) {
    log.success(`Valid variables (${serverResults.valid.length}): ${serverResults.valid.join(', ')}`);
  }
  
  if (serverResults.missing.length > 0) {
    log.error(`Missing variables (${serverResults.missing.length}): ${serverResults.missing.join(', ')}`);
  }
  
  if (serverResults.invalid.length > 0) {
    log.error('Invalid variable formats:');
    serverResults.invalid.forEach(({ name, value, pattern }) => {
      console.log(`  ${name}: "${value}" (expected pattern: ${pattern})`);
    });
  }
  
  if (serverResults.warnings.length > 0) {
    serverResults.warnings.forEach(warning => log.warning(warning));
  }
  
  console.log('');
  
  // Validate web environment
  log.title('🌐 Web Environment Validation');
  const webRequired = requiredVars[environment]?.web || requiredVars.development.web;
  const webResults = validateEnvironment(webEnv, webRequired, 'web');
  
  // Report web results
  if (webResults.valid.length > 0) {
    log.success(`Valid variables (${webResults.valid.length}): ${webResults.valid.join(', ')}`);
  }
  
  if (webResults.missing.length > 0) {
    log.error(`Missing variables (${webResults.missing.length}): ${webResults.missing.join(', ')}`);
  }
  
  if (webResults.invalid.length > 0) {
    log.error('Invalid variable formats:');
    webResults.invalid.forEach(({ name, value, pattern }) => {
      console.log(`  ${name}: "${value}" (expected pattern: ${pattern})`);
    });
  }
  
  console.log('');
  
  // Test service connections
  log.title('🔗 Service Connection Tests');
  
  const supabaseUrl = serverEnv.SUPABASE_URL || webEnv.VITE_SUPABASE_URL;
  const anonKey = serverEnv.SUPABASE_ANON_KEY || webEnv.VITE_SUPABASE_ANON_KEY;
  const openaiKey = serverEnv.OPENAI_API_KEY;
  
  if (supabaseUrl && anonKey) {
    log.info('Testing Supabase connection...');
    const supabaseOk = await checkSupabaseConnection(supabaseUrl, anonKey);
    if (supabaseOk) {
      log.success('Supabase connection successful');
    } else {
      log.error('Supabase connection failed');
    }
  } else {
    log.warning('Skipping Supabase connection test (missing credentials)');
  }
  
  if (openaiKey && environment !== 'test') {
    log.info('Testing OpenAI API connection...');
    const openaiOk = await checkOpenAIKey(openaiKey);
    if (openaiOk) {
      log.success('OpenAI API connection successful');
    } else {
      log.error('OpenAI API connection failed');
    }
  } else {
    log.warning('Skipping OpenAI API test (missing key or test environment)');
  }
  
  console.log('');
  
  // Summary
  log.title('📋 Validation Summary');
  
  const totalMissing = serverResults.missing.length + webResults.missing.length;
  const totalInvalid = serverResults.invalid.length + webResults.invalid.length;
  const totalWarnings = serverResults.warnings.length + webResults.warnings.length;
  
  if (totalMissing === 0 && totalInvalid === 0) {
    log.success('✅ All required environment variables are properly configured');
  } else {
    log.error(`❌ Found ${totalMissing} missing and ${totalInvalid} invalid variables`);
  }
  
  if (totalWarnings > 0) {
    log.warning(`⚠️  ${totalWarnings} warnings found`);
  }
  
  // Optional variables info
  if (environment === 'development') {
    console.log('');
    log.title('💡 Optional Variables');
    Object.entries(optionalVars).forEach(([key, description]) => {
      const hasValue = serverEnv[key] && serverEnv[key] !== 'your-key-here';
      const status = hasValue ? '✓' : '○';
      console.log(`  ${status} ${key}: ${description}`);
    });
  }
  
  console.log('');
  
  // Exit with appropriate code
  if (totalMissing > 0 || totalInvalid > 0) {
    log.error('Environment validation failed. Please fix the issues above.');
    process.exit(1);
  } else {
    log.success('Environment validation passed! 🎉');
    process.exit(0);
  }
}

// Handle command line arguments
const args = process.argv.slice(2);
if (args.includes('--help') || args.includes('-h')) {
  console.log('Usage: node scripts/validate-env.js [options]');
  console.log('');
  console.log('Options:');
  console.log('  --help, -h     Show this help message');
  console.log('');
  console.log('This script validates environment variables for the IBC-CIE project.');
  console.log('It checks for required variables, validates formats, and tests service connections.');
  process.exit(0);
}

// Run validation
validateSetup().catch(error => {
  log.error(`Validation failed: ${error.message}`);
  process.exit(1);
});