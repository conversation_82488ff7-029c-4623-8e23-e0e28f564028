#!/bin/bash

# IBC-CIE Health Check Script
# This script performs comprehensive health checks on all services

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
BOLD='\033[1m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_title() {
    echo -e "${BOLD}${BLUE}$1${NC}"
}

# Health check results
declare -A health_results
total_checks=0
passed_checks=0

# Function to record health check result
record_check() {
    local service="$1"
    local status="$2"
    local message="$3"
    
    health_results["$service"]="$status:$message"
    total_checks=$((total_checks + 1))
    
    if [ "$status" = "PASS" ]; then
        passed_checks=$((passed_checks + 1))
        print_success "✅ $service: $message"
    elif [ "$status" = "WARN" ]; then
        print_warning "⚠️  $service: $message"
    else
        print_error "❌ $service: $message"
    fi
}

# Check if a service is responding
check_http_service() {
    local name="$1"
    local url="$2"
    local timeout="${3:-5}"
    
    if curl -s --max-time "$timeout" "$url" > /dev/null 2>&1; then
        record_check "$name" "PASS" "Service responding at $url"
    else
        record_check "$name" "FAIL" "Service not responding at $url"
    fi
}

# Check if a port is open
check_port() {
    local name="$1"
    local host="$2"
    local port="$3"
    
    if nc -z -w1 "$host" "$port" 2>/dev/null; then
        record_check "$name" "PASS" "Port $port is open on $host"
    else
        record_check "$name" "FAIL" "Port $port is not accessible on $host"
    fi
}

# Check Supabase services
check_supabase() {
    print_title "🗄️  Checking Supabase Services"
    
    # Check if Supabase CLI is available
    if command -v supabase &> /dev/null; then
        record_check "Supabase CLI" "PASS" "CLI is installed"
        
        # Check Supabase status
        if supabase status &> /dev/null; then
            record_check "Supabase Status" "PASS" "Services are running"
            
            # Check individual services
            check_http_service "Supabase API" "http://127.0.0.1:54321/health"
            check_http_service "Supabase Studio" "http://127.0.0.1:54323"
            check_port "PostgreSQL" "127.0.0.1" "54322"
            check_port "Inbucket" "127.0.0.1" "54324"
            
            # Check database connectivity
            if psql "postgresql://postgres:postgres@127.0.0.1:54322/postgres" -c "SELECT 1;" &> /dev/null; then
                record_check "Database Connection" "PASS" "Can connect to PostgreSQL"
                
                # Check if tables exist
                table_count=$(psql "postgresql://postgres:postgres@127.0.0.1:54322/postgres" -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" 2>/dev/null | xargs || echo "0")
                if [ "$table_count" -gt 0 ]; then
                    record_check "Database Schema" "PASS" "$table_count tables found"
                else
                    record_check "Database Schema" "WARN" "No tables found - run migrations"
                fi
            else
                record_check "Database Connection" "FAIL" "Cannot connect to PostgreSQL"
            fi
        else
            record_check "Supabase Status" "FAIL" "Services not running - run 'pnpm supabase:start'"
        fi
    else
        record_check "Supabase CLI" "FAIL" "CLI not installed"
    fi
    
    echo ""
}

# Check Node.js environment
check_nodejs() {
    print_title "📦 Checking Node.js Environment"
    
    # Check Node.js version
    if command -v node &> /dev/null; then
        node_version=$(node --version)
        major_version=$(echo "$node_version" | sed 's/v\([0-9]*\).*/\1/')
        
        if [ "$major_version" -ge 18 ]; then
            record_check "Node.js Version" "PASS" "$node_version"
        else
            record_check "Node.js Version" "WARN" "$node_version (recommend 18+)"
        fi
    else
        record_check "Node.js" "FAIL" "Node.js not installed"
    fi
    
    # Check package manager
    if command -v pnpm &> /dev/null; then
        pnpm_version=$(pnpm --version)
        record_check "pnpm" "PASS" "v$pnpm_version"
    elif command -v npm &> /dev/null; then
        npm_version=$(npm --version)
        record_check "npm" "PASS" "v$npm_version (consider using pnpm)"
    else
        record_check "Package Manager" "FAIL" "No package manager found"
    fi
    
    # Check if dependencies are installed
    if [ -d "node_modules" ]; then
        record_check "Dependencies" "PASS" "node_modules found"
    else
        record_check "Dependencies" "FAIL" "Run 'pnpm install'"
    fi
    
    echo ""
}

# Check development servers
check_dev_servers() {
    print_title "🖥️  Checking Development Servers"
    
    # Check if development servers are running
    check_http_service "Web Server" "http://localhost:5174" 3
    check_http_service "API Server" "http://localhost:3000" 3
    
    # Check for common development processes
    if pgrep -f "react-router dev" > /dev/null; then
        record_check "Web Dev Process" "PASS" "React Router dev server running"
    else
        record_check "Web Dev Process" "WARN" "React Router dev server not running"
    fi
    
    if pgrep -f "next dev" > /dev/null; then
        record_check "API Dev Process" "PASS" "Next.js dev server running"
    else
        record_check "API Dev Process" "WARN" "Next.js dev server not running"
    fi
    
    echo ""
}

# Check environment configuration
check_environment() {
    print_title "⚙️  Checking Environment Configuration"
    
    # Check for environment files
    if [ -f ".env.local" ]; then
        record_check "Environment File" "PASS" ".env.local found"
        
        # Basic validation of required variables
        if grep -q "SUPABASE_URL" .env.local && grep -q "SUPABASE_ANON_KEY" .env.local; then
            record_check "Supabase Config" "PASS" "Basic Supabase variables found"
        else
            record_check "Supabase Config" "FAIL" "Missing Supabase variables"
        fi
        
        if grep -q "OPENAI_API_KEY" .env.local; then
            openai_key=$(grep "OPENAI_API_KEY" .env.local | cut -d'=' -f2)
            if [[ "$openai_key" == *"your-openai-key"* ]]; then
                record_check "OpenAI Config" "WARN" "Default OpenAI key detected"
            else
                record_check "OpenAI Config" "PASS" "OpenAI key configured"
            fi
        else
            record_check "OpenAI Config" "WARN" "OpenAI key not found"
        fi
    else
        record_check "Environment File" "FAIL" ".env.local not found"
    fi
    
    # Check TypeScript types
    if [ -f "apps/server/src/types/supabase.ts" ]; then
        record_check "TypeScript Types" "PASS" "Supabase types generated"
    else
        record_check "TypeScript Types" "WARN" "Run 'pnpm db:types' to generate types"
    fi
    
    echo ""
}

# Check external services
check_external_services() {
    print_title "🌐 Checking External Services"
    
    # Check internet connectivity
    if ping -c 1 google.com &> /dev/null; then
        record_check "Internet" "PASS" "Internet connectivity available"
        
        # Check OpenAI API (if key is configured)
        if [ -f ".env.local" ] && grep -q "OPENAI_API_KEY" .env.local; then
            openai_key=$(grep "OPENAI_API_KEY" .env.local | cut -d'=' -f2)
            if [[ "$openai_key" != *"your-openai-key"* ]] && [[ "$openai_key" == sk-* ]]; then
                if curl -s --max-time 5 "https://api.openai.com/v1/models" -H "Authorization: Bearer $openai_key" > /dev/null; then
                    record_check "OpenAI API" "PASS" "API key is valid"
                else
                    record_check "OpenAI API" "FAIL" "API key is invalid or API unreachable"
                fi
            else
                record_check "OpenAI API" "WARN" "API key not configured"
            fi
        else
            record_check "OpenAI API" "WARN" "API key not found"
        fi
    else
        record_check "Internet" "FAIL" "No internet connectivity"
    fi
    
    echo ""
}

# Check Docker
check_docker() {
    print_title "🐳 Checking Docker"
    
    if command -v docker &> /dev/null; then
        record_check "Docker CLI" "PASS" "Docker is installed"
        
        if docker info &> /dev/null; then
            record_check "Docker Daemon" "PASS" "Docker daemon is running"
            
            # Check Docker resources
            containers=$(docker ps -q | wc -l | xargs)
            record_check "Docker Containers" "PASS" "$containers containers running"
        else
            record_check "Docker Daemon" "FAIL" "Docker daemon not running"
        fi
    else
        record_check "Docker CLI" "FAIL" "Docker not installed"
    fi
    
    echo ""
}

# Performance checks
check_performance() {
    print_title "⚡ Performance Checks"
    
    # Check available disk space
    disk_usage=$(df -h . | tail -1 | awk '{print $5}' | sed 's/%//')
    if [ "$disk_usage" -lt 90 ]; then
        record_check "Disk Space" "PASS" "${disk_usage}% used"
    elif [ "$disk_usage" -lt 95 ]; then
        record_check "Disk Space" "WARN" "${disk_usage}% used"
    else
        record_check "Disk Space" "FAIL" "${disk_usage}% used (critical)"
    fi
    
    # Check available memory (macOS/Linux)
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        memory_pressure=$(memory_pressure | grep "System-wide memory free percentage" | awk '{print $5}' | sed 's/%//' || echo "unknown")
        if [[ "$memory_pressure" != "unknown" ]] && [ "$memory_pressure" -gt 20 ]; then
            record_check "Memory" "PASS" "${memory_pressure}% free"
        else
            record_check "Memory" "WARN" "Low memory available"
        fi
    else
        # Linux
        if command -v free &> /dev/null; then
            memory_free=$(free | grep Mem | awk '{printf "%.0f", $7/$2 * 100.0}')
            if [ "$memory_free" -gt 20 ]; then
                record_check "Memory" "PASS" "${memory_free}% available"
            else
                record_check "Memory" "WARN" "Low memory available"
            fi
        else
            record_check "Memory" "WARN" "Cannot check memory usage"
        fi
    fi
    
    echo ""
}

# Generate health report
generate_report() {
    print_title "📊 Health Check Summary"
    
    echo "Overall Status: $passed_checks/$total_checks checks passed"
    echo ""
    
    # Calculate health percentage
    if [ "$total_checks" -gt 0 ]; then
        health_percentage=$((passed_checks * 100 / total_checks))
        
        if [ "$health_percentage" -ge 90 ]; then
            print_success "System Health: ${health_percentage}% - Excellent"
        elif [ "$health_percentage" -ge 70 ]; then
            print_warning "System Health: ${health_percentage}% - Good"
        elif [ "$health_percentage" -ge 50 ]; then
            print_warning "System Health: ${health_percentage}% - Fair"
        else
            print_error "System Health: ${health_percentage}% - Poor"
        fi
    fi
    
    echo ""
    
    # Show failed checks
    echo "Failed Checks:"
    for service in "${!health_results[@]}"; do
        IFS=':' read -ra parts <<< "${health_results[$service]}"
        status="${parts[0]}"
        message="${parts[1]}"
        
        if [ "$status" = "FAIL" ]; then
            echo "  ❌ $service: $message"
        fi
    done
    
    echo ""
    
    # Show warnings
    echo "Warnings:"
    for service in "${!health_results[@]}"; do
        IFS=':' read -ra parts <<< "${health_results[$service]}"
        status="${parts[0]}"
        message="${parts[1]}"
        
        if [ "$status" = "WARN" ]; then
            echo "  ⚠️  $service: $message"
        fi
    done
    
    echo ""
    
    # Recommendations
    if [ "$health_percentage" -lt 100 ]; then
        print_title "🔧 Recommendations"
        
        if [[ "${health_results[*]}" == *"FAIL"* ]]; then
            echo "• Fix critical issues marked with ❌"
        fi
        
        if [[ "${health_results[*]}" == *"Services not running"* ]]; then
            echo "• Run 'pnpm supabase:start' to start Supabase services"
        fi
        
        if [[ "${health_results[*]}" == *"Dev server not running"* ]]; then
            echo "• Run 'pnpm dev' to start development servers"
        fi
        
        if [[ "${health_results[*]}" == *"Dependencies"* ]]; then
            echo "• Run 'pnpm install' to install dependencies"
        fi
        
        if [[ "${health_results[*]}" == *"Environment File"* ]]; then
            echo "• Copy .env.local.example to .env.local and configure"
        fi
        
        if [[ "${health_results[*]}" == *"TypeScript Types"* ]]; then
            echo "• Run 'pnpm db:types' to generate TypeScript types"
        fi
    else
        print_success "All systems operational! 🎉"
    fi
    
    echo ""
}

# Main execution
main() {
    echo "🏥 IBC-CIE Health Check"
    echo "Performing comprehensive system health checks..."
    echo ""
    
    check_nodejs
    check_docker
    check_supabase
    check_environment
    check_dev_servers
    check_external_services
    check_performance
    generate_report
    
    # Exit with appropriate code
    if [ "$passed_checks" -eq "$total_checks" ]; then
        exit 0
    else
        exit 1
    fi
}

# Handle command line arguments
case "${1:-}" in
    --help|-h)
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h     Show this help message"
        echo ""
        echo "This script performs comprehensive health checks on the IBC-CIE development environment."
        echo "It verifies that all required services are running and properly configured."
        ;;
    *)
        main "$@"
        ;;
esac