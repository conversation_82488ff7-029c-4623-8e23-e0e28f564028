#!/usr/bin/env node

/**
 * Mock Data Seeder for IBC-CIE Supabase Database
 * 
 * This script populates the Supabase database with realistic mock data
 * for development and testing purposes.
 * 
 * Usage:
 *   node scripts/seed-mock-data.js
 *   
 * Environment Variables Required:
 *   - SUPABASE_URL
 *   - SUPABASE_SERVICE_ROLE_KEY
 */

import { createClient } from '@supabase/supabase-js'
import { config } from 'dotenv'
import { fileURLToPath } from 'url'
import { dirname, join } from 'path'

// Load environment variables from .env.local
const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)
const rootDir = join(__dirname, '..')

config({ path: join(rootDir, '.env.local') })

// Configuration
const SUPABASE_URL = process.env.SUPABASE_URL
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
  console.error('❌ Missing required environment variables:')
  console.error('   SUPABASE_URL:', SUPABASE_URL ? '✓' : '❌')
  console.error('   SUPABASE_SERVICE_ROLE_KEY:', SUPABASE_SERVICE_KEY ? '✓' : '❌')
  process.exit(1)
}

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY)

// Mock Data Definitions
const mockUsers = [
  {
    id: '550e8400-e29b-41d4-a716-446655440000',
    email: '<EMAIL>',
    name: 'Admin User',
    is_kyc_verified: true,
    auth_user_id: 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11'
  },
  {
    id: '550e8400-e29b-41d4-a716-************',
    email: '<EMAIL>', 
    name: 'Alice Johnson',
    is_kyc_verified: true,
    auth_user_id: 'b1ffdc00-ad1c-5ef9-cc7e-7cc0ce491b22'
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440002',
    email: '<EMAIL>',
    name: 'Bob Smith',
    is_kyc_verified: false,
    auth_user_id: 'c2ggdd11-be2d-6fga-dd8f-8dd1df592c33'
  }
]

const mockProjects = [
  {
    id: '6ba7b810-9dad-11d1-80b4-00c04fd430c8',
    title: 'Cosmos Hub',
    description: 'The Cosmos Hub is the first blockchain in the Cosmos Network, designed to facilitate interoperability between blockchains through the Inter-Blockchain Communication (IBC) protocol.',
    image_url: 'https://example.com/cosmos-hub.png',
    website_url: 'https://hub.cosmos.network',
    deck_url: 'https://example.com/cosmos-deck.pdf',
    whitepaper_url: 'https://example.com/cosmos-whitepaper.pdf',
    social_urls: {
      twitter: 'https://twitter.com/cosmoshub',
      discord: 'https://discord.gg/cosmosnetwork',
      telegram: 'https://t.me/cosmosproject'
    },
    challenge_intro: 'As the foundational blockchain of the Cosmos ecosystem, how effectively does Cosmos Hub serve as the central hub for inter-blockchain communication and security?',
    is_approved_for_voting: true
  },
  {
    id: '6ba7b811-9dad-11d1-80b4-00c04fd430c8',
    title: 'Osmosis',
    description: 'Osmosis is an advanced automated market maker (AMM) protocol built using the Cosmos SDK that allows developers to design, build, and deploy their own customized AMMs.',
    image_url: 'https://example.com/osmosis.png',
    website_url: 'https://osmosis.zone',
    deck_url: 'https://example.com/osmosis-deck.pdf',
    whitepaper_url: 'https://example.com/osmosis-whitepaper.pdf',
    social_urls: {
      twitter: 'https://twitter.com/OsmosisZone',
      discord: 'https://discord.gg/osmosis',
      telegram: 'https://t.me/osmosis_chat'
    },
    challenge_intro: 'How well does Osmosis serve as the premier decentralized exchange and liquidity hub within the Cosmos ecosystem?',
    is_approved_for_voting: true
  },
  {
    id: '6ba7b812-9dad-11d1-80b4-00c04fd430c8',
    title: 'Juno Network',
    description: 'Juno is a global, open source, permission-less network for decentralized interoperable applications. The network serves as a decentralized, censorship resistant way for developers to efficiently and securely launch smart contracts using proven frameworks and compile them in various languages.',
    image_url: 'https://example.com/juno.png',
    website_url: 'https://junonetwork.io',
    deck_url: 'https://example.com/juno-deck.pdf',
    whitepaper_url: 'https://example.com/juno-whitepaper.pdf',
    social_urls: {
      twitter: 'https://twitter.com/JunoNetwork',
      discord: 'https://discord.gg/juno',
      telegram: 'https://t.me/JunoNetwork'
    },
    challenge_intro: 'How effectively does Juno Network provide a robust and developer-friendly platform for smart contract deployment in the Cosmos ecosystem?',
    is_approved_for_voting: true
  },
  {
    id: '6ba7b813-9dad-11d1-80b4-00c04fd430c8',
    title: 'Secret Network',
    description: 'Secret Network is a blockchain with data privacy by default, allowing you to build and use applications that are both permissionless and privacy-preserving. This unique functionality protects users, secures applications, and unlocks hundreds of never-before-possible use cases for Web3.',
    image_url: 'https://example.com/secret.png',
    website_url: 'https://scrt.network',
    deck_url: 'https://example.com/secret-deck.pdf',
    whitepaper_url: 'https://example.com/secret-whitepaper.pdf',
    social_urls: {
      twitter: 'https://twitter.com/SecretNetwork',
      discord: 'https://discord.gg/secret',
      telegram: 'https://t.me/SCRTcommunity'
    },
    challenge_intro: 'How well does Secret Network balance privacy, functionality, and decentralization in its privacy-preserving blockchain architecture?',
    is_approved_for_voting: true
  },
  {
    id: '6ba7b814-9dad-11d1-80b4-00c04fd430c8',
    title: 'Akash Network',
    description: 'Akash Network is the world\'s first decentralized cloud computing marketplace, enabling any person to buy and sell computing resources securely and efficiently. Purpose-built for public utility.',
    image_url: 'https://example.com/akash.png',
    website_url: 'https://akash.network',
    deck_url: 'https://example.com/akash-deck.pdf',
    whitepaper_url: 'https://example.com/akash-whitepaper.pdf',
    social_urls: {
      twitter: 'https://twitter.com/akashnet_',
      discord: 'https://discord.gg/akash',
      telegram: 'https://t.me/AkashNW'
    },
    challenge_intro: 'How effectively does Akash Network provide a viable decentralized alternative to traditional cloud computing services?',
    is_approved_for_voting: true
  }
]

const mockReviews = [
  {
    id: '7ca7c820-aead-22e2-91c5-11d14fe541d9',
    user_id: '550e8400-e29b-41d4-a716-************',
    project_id: '6ba7b810-9dad-11d1-80b4-00c04fd430c8',
    overall_sentiment: true,
    overall_comments: 'Cosmos Hub has established itself as the cornerstone of interoperability in blockchain. The IBC protocol is revolutionary and the validator set is robust. However, there are concerns about governance efficiency and the need for more active development.',
    overall_comments_relevant: true,
    overall_relevance_score: 85
  },
  {
    id: '7ca7c821-aead-22e2-91c5-11d14fe541d9',
    user_id: '550e8400-e29b-41d4-a716-************',
    project_id: '6ba7b811-9dad-11d1-80b4-00c04fd430c8',
    overall_sentiment: true,
    overall_comments: 'Osmosis has created an innovative AMM design with concentrated liquidity and superfluid staking. The user experience is excellent and trading fees are competitive. The main concern is the complexity for new users and potential smart contract risks.',
    overall_comments_relevant: true,
    overall_relevance_score: 90
  },
  {
    id: '7ca7c822-aead-22e2-91c5-11d14fe541d9',
    user_id: '550e8400-e29b-41d4-a716-************',
    project_id: '6ba7b812-9dad-11d1-80b4-00c04fd430c8',
    overall_sentiment: false,
    overall_comments: 'While Juno has good developer tools and CosmWasm integration, the network has faced significant challenges with governance controversies and validator centralization. The recent proposals and their handling raise concerns about true decentralization.',
    overall_comments_relevant: true,
    overall_relevance_score: 75
  }
]

const mockReviewResponses = [
  // Cosmos Hub responses
  { id: '8da8d930-bfbe-33f3-a2d6-22e25gf652ea', review_id: '7ca7c820-aead-22e2-91c5-11d14fe541d9', dimension: 'PROJECT_FUNDAMENTALS', question_index: 0, vote: true, feedback: 'Strong foundation with proven IBC protocol and clear utility as the hub for Cosmos ecosystem', feedback_relevant: true, relevance_score: 90 },
  { id: '8da8d931-bfbe-33f3-a2d6-22e25gf652eb', review_id: '7ca7c820-aead-22e2-91c5-11d14fe541d9', dimension: 'PROJECT_FUNDAMENTALS', question_index: 1, vote: true, feedback: 'Well-defined use case as the interoperability hub', feedback_relevant: true, relevance_score: 85 },
  { id: '8da8d932-bfbe-33f3-a2d6-22e25gf652ec', review_id: '7ca7c820-aead-22e2-91c5-11d14fe541d9', dimension: 'TEAM_GOVERNANCE', question_index: 0, vote: false, feedback: 'Governance processes can be slow and sometimes contentious', feedback_relevant: true, relevance_score: 70 },
  { id: '8da8d933-bfbe-33f3-a2d6-22e25gf652ed', review_id: '7ca7c820-aead-22e2-91c5-11d14fe541d9', dimension: 'TEAM_GOVERNANCE', question_index: 1, vote: true, feedback: 'Core development team is experienced and competent', feedback_relevant: true, relevance_score: 80 },
  { id: '8da8d934-bfbe-33f3-a2d6-22e25gf652ee', review_id: '7ca7c820-aead-22e2-91c5-11d14fe541d9', dimension: 'TRANSPARENCY_DOCUMENTATION', question_index: 0, vote: true, feedback: 'Good documentation and transparent development process', feedback_relevant: true, relevance_score: 85 },
  { id: '8da8d935-bfbe-33f3-a2d6-22e25gf652ef', review_id: '7ca7c820-aead-22e2-91c5-11d14fe541d9', dimension: 'TECHNOLOGY_EXECUTION', question_index: 0, vote: true, feedback: 'IBC protocol works well and continues to improve', feedback_relevant: true, relevance_score: 90 },
  { id: '8da8d936-bfbe-33f3-a2d6-22e25gf652eg', review_id: '7ca7c820-aead-22e2-91c5-11d14fe541d9', dimension: 'COMMUNITY_COMMUNICATION', question_index: 0, vote: true, feedback: 'Active community and regular updates', feedback_relevant: true, relevance_score: 80 },
  { id: '8da8d937-bfbe-33f3-a2d6-22e25gf652eh', review_id: '7ca7c820-aead-22e2-91c5-11d14fe541d9', dimension: 'TOKEN_UTILITY_TOKENOMICS', question_index: 0, vote: true, feedback: 'ATOM has clear utility for staking and governance', feedback_relevant: true, relevance_score: 85 },

  // Osmosis responses
  { id: '8da8d940-bfbe-33f3-a2d6-22e25gf652ei', review_id: '7ca7c821-aead-22e2-91c5-11d14fe541d9', dimension: 'PROJECT_FUNDAMENTALS', question_index: 0, vote: true, feedback: 'Innovative AMM design with concentrated liquidity and superfluid staking', feedback_relevant: true, relevance_score: 95 },
  { id: '8da8d941-bfbe-33f3-a2d6-22e25gf652ej', review_id: '7ca7c821-aead-22e2-91c5-11d14fe541d9', dimension: 'PROJECT_FUNDAMENTALS', question_index: 1, vote: true, feedback: 'Clear value proposition as the main DEX for Cosmos', feedback_relevant: true, relevance_score: 90 },
  { id: '8da8d942-bfbe-33f3-a2d6-22e25gf652ek', review_id: '7ca7c821-aead-22e2-91c5-11d14fe541d9', dimension: 'TEAM_GOVERNANCE', question_index: 0, vote: true, feedback: 'Strong development team with good governance practices', feedback_relevant: true, relevance_score: 85 },
  { id: '8da8d943-bfbe-33f3-a2d6-22e25gf652el', review_id: '7ca7c821-aead-22e2-91c5-11d14fe541d9', dimension: 'TRANSPARENCY_DOCUMENTATION', question_index: 0, vote: true, feedback: 'Excellent documentation and transparent roadmap', feedback_relevant: true, relevance_score: 90 },
  { id: '8da8d944-bfbe-33f3-a2d6-22e25gf652em', review_id: '7ca7c821-aead-22e2-91c5-11d14fe541d9', dimension: 'TECHNOLOGY_EXECUTION', question_index: 0, vote: true, feedback: 'Complex but well-executed technology stack', feedback_relevant: true, relevance_score: 90 },
  { id: '8da8d945-bfbe-33f3-a2d6-22e25gf652en', review_id: '7ca7c821-aead-22e2-91c5-11d14fe541d9', dimension: 'COMMUNITY_COMMUNICATION', question_index: 0, vote: true, feedback: 'Very active community and great user experience', feedback_relevant: true, relevance_score: 95 },
  { id: '8da8d946-bfbe-33f3-a2d6-22e25gf652eo', review_id: '7ca7c821-aead-22e2-91c5-11d14fe541d9', dimension: 'TOKEN_UTILITY_TOKENOMICS', question_index: 0, vote: true, feedback: 'OSMO token has multiple utilities and good tokenomics', feedback_relevant: true, relevance_score: 90 },

  // Juno responses
  { id: '8da8d950-bfbe-33f3-a2d6-22e25gf652ep', review_id: '7ca7c822-aead-22e2-91c5-11d14fe541d9', dimension: 'PROJECT_FUNDAMENTALS', question_index: 0, vote: true, feedback: 'Good smart contract platform with CosmWasm integration', feedback_relevant: true, relevance_score: 80 },
  { id: '8da8d951-bfbe-33f3-a2d6-22e25gf652eq', review_id: '7ca7c822-aead-22e2-91c5-11d14fe541d9', dimension: 'TEAM_GOVERNANCE', question_index: 0, vote: false, feedback: 'Recent governance controversies and validator centralization issues', feedback_relevant: true, relevance_score: 60 },
  { id: '8da8d952-bfbe-33f3-a2d6-22e25gf652er', review_id: '7ca7c822-aead-22e2-91c5-11d14fe541d9', dimension: 'TRANSPARENCY_DOCUMENTATION', question_index: 0, vote: false, feedback: 'Some controversial decisions lacked transparency', feedback_relevant: true, relevance_score: 65 },
  { id: '8da8d953-bfbe-33f3-a2d6-22e25gf652es', review_id: '7ca7c822-aead-22e2-91c5-11d14fe541d9', dimension: 'TECHNOLOGY_EXECUTION', question_index: 0, vote: true, feedback: 'Solid technical execution with CosmWasm', feedback_relevant: true, relevance_score: 85 },
  { id: '8da8d954-bfbe-33f3-a2d6-22e25gf652et', review_id: '7ca7c822-aead-22e2-91c5-11d14fe541d9', dimension: 'COMMUNITY_COMMUNICATION', question_index: 0, vote: false, feedback: 'Community trust has been damaged by recent events', feedback_relevant: true, relevance_score: 50 },
  { id: '8da8d955-bfbe-33f3-a2d6-22e25gf652eu', review_id: '7ca7c822-aead-22e2-91c5-11d14fe541d9', dimension: 'TOKEN_UTILITY_TOKENOMICS', question_index: 0, vote: true, feedback: 'JUNO token mechanics are sound', feedback_relevant: true, relevance_score: 75 }
]

const mockAIAnalyses = [
  {
    id: '9ea9ea40-cfcf-44g4-b3e7-33f36hg763fb',
    project_id: '6ba7b810-9dad-11d1-80b4-00c04fd430c8',
    project_fundamentals_score: 88,
    team_governance_score: 75,
    transparency_doc_score: 85,
    technology_execution_score: 90,
    community_communication_score: 80,
    token_utility_tokenomics_score: 85,
    overall_score: 84,
    analysis: 'Cosmos Hub represents a foundational piece of blockchain infrastructure with strong technical merit. The IBC protocol is a significant innovation that enables true interoperability between blockchains. However, governance challenges and the need for more active development pose ongoing concerns.',
    reasoning: 'The analysis is based on the project\'s pioneering role in blockchain interoperability, strong technical foundations, but balanced against governance inefficiencies and development pace concerns raised by the community.',
    strengths: ['First mover in blockchain interoperability', 'Proven IBC protocol', 'Strong validator network', 'Clear utility as ecosystem hub'],
    weaknesses: ['Governance can be slow and contentious', 'Development pace concerns', 'Competition from other ecosystems', 'Complexity for new users'],
    recommendations: ['Improve governance efficiency', 'Accelerate development roadmap', 'Better onboarding for new users', 'Enhanced developer tools'],
    confidence: 85
  },
  {
    id: '9ea9ea41-cfcf-44g4-b3e7-33f36hg763fc',
    project_id: '6ba7b811-9dad-11d1-80b4-00c04fd430c8',
    project_fundamentals_score: 92,
    team_governance_score: 85,
    transparency_doc_score: 90,
    technology_execution_score: 90,
    community_communication_score: 95,
    token_utility_tokenomics_score: 90,
    overall_score: 90,
    analysis: 'Osmosis has established itself as the premier DEX in the Cosmos ecosystem with innovative features like concentrated liquidity and superfluid staking. The project demonstrates strong execution, community engagement, and continuous innovation.',
    reasoning: 'High scores reflect the project\'s technical innovation, strong community adoption, excellent user experience, and clear value proposition within the Cosmos ecosystem.',
    strengths: ['Innovative AMM design', 'Excellent user experience', 'Strong community', 'Continuous development', 'Multiple revenue streams'],
    weaknesses: ['Complexity for new users', 'Smart contract risks', 'Dependency on Cosmos ecosystem', 'Competition from other DEXs'],
    recommendations: ['Simplify user interface', 'Enhanced security audits', 'Cross-chain expansion', 'Educational content'],
    confidence: 90
  },
  {
    id: '9ea9ea42-cfcf-44g4-b3e7-33f36hg763fd',
    project_id: '6ba7b812-9dad-11d1-80b4-00c04fd430c8',
    project_fundamentals_score: 80,
    team_governance_score: 60,
    transparency_doc_score: 65,
    technology_execution_score: 85,
    community_communication_score: 50,
    token_utility_tokenomics_score: 75,
    overall_score: 69,
    analysis: 'Juno Network shows strong technical capabilities with CosmWasm integration but has faced significant governance and community challenges. Recent controversies have impacted trust and raised questions about decentralization.',
    reasoning: 'Mixed scores reflect strong technical foundations balanced against significant governance issues, community trust problems, and centralization concerns that have emerged over time.',
    strengths: ['Good developer tools', 'CosmWasm integration', 'Technical competence', 'Smart contract capabilities'],
    weaknesses: ['Governance controversies', 'Validator centralization', 'Community trust issues', 'Transparency concerns'],
    recommendations: ['Address governance issues', 'Improve validator decentralization', 'Rebuild community trust', 'Enhance transparency'],
    confidence: 75
  }
]

// Utility Functions
async function clearData() {
  console.log('🧹 Clearing existing data...')
  
  const tables = ['ai_analyses', 'review_responses', 'reviews', 'projects', 'users']
  
  for (const table of tables) {
    const { error } = await supabase
      .from(table)
      .delete()
      .neq('id', '') // Delete all records
    
    if (error && error.code !== 'PGRST116') { // Ignore "no rows found" error
      console.error(`❌ Error clearing ${table}:`, error.message)
    } else {
      console.log(`✅ Cleared ${table}`)
    }
  }
}

async function insertData(tableName, data, description) {
  console.log(`📝 Inserting ${description}...`)
  
  const { data: insertedData, error } = await supabase
    .from(tableName)
    .insert(data)
    .select()
  
  if (error) {
    console.error(`❌ Error inserting ${description}:`, error.message)
    throw error
  }
  
  console.log(`✅ Inserted ${insertedData.length} ${description}`)
  return insertedData
}

async function validateData() {
  console.log('🔍 Validating inserted data...')
  
  const tables = [
    { name: 'users', expected: mockUsers.length },
    { name: 'projects', expected: mockProjects.length },
    { name: 'reviews', expected: mockReviews.length },
    { name: 'review_responses', expected: mockReviewResponses.length },
    { name: 'ai_analyses', expected: mockAIAnalyses.length }
  ]
  
  for (const table of tables) {
    const { count, error } = await supabase
      .from(table.name)
      .select('*', { count: 'exact', head: true })
    
    if (error) {
      console.error(`❌ Error counting ${table.name}:`, error.message)
    } else if (count === table.expected) {
      console.log(`✅ ${table.name}: ${count}/${table.expected} records`)
    } else {
      console.warn(`⚠️  ${table.name}: ${count}/${table.expected} records (mismatch)`)
    }
  }
}

// Main execution
async function main() {
  try {
    console.log('🚀 Starting mock data seeding...')
    console.log('📡 Supabase URL:', SUPABASE_URL.replace(/\/+$/, ''))
    
    // Test connection
    const { data: healthCheck, error: healthError } = await supabase
      .from('users')
      .select('count', { count: 'exact', head: true })
    
    if (healthError) {
      throw new Error(`Database connection failed: ${healthError.message}`)
    }
    
    console.log('✅ Database connection successful')
    
    // Clear existing data
    await clearData()
    
    // Insert new data in dependency order
    await insertData('users', mockUsers, 'users')
    await insertData('projects', mockProjects, 'projects')
    await insertData('reviews', mockReviews, 'reviews')
    await insertData('review_responses', mockReviewResponses, 'review responses')
    await insertData('ai_analyses', mockAIAnalyses, 'AI analyses')
    
    // Validate the results
    await validateData()
    
    console.log('')
    console.log('🎉 Mock data seeding completed successfully!')
    console.log('')
    console.log('📊 Summary:')
    console.log(`   👤 Users: ${mockUsers.length} (1 admin, 1 KYC verified, 1 pending)`)
    console.log(`   🏗️  Projects: ${mockProjects.length} (all approved for voting)`)
    console.log(`   📝 Reviews: ${mockReviews.length} (from Alice across 3 projects)`)
    console.log(`   💬 Review Responses: ${mockReviewResponses.length} (detailed feedback per dimension)`)
    console.log(`   🤖 AI Analyses: ${mockAIAnalyses.length} (comprehensive scoring)`)
    console.log('')
    console.log('🧪 Test Users:')
    console.log('   <EMAIL> (Admin, KYC ✓)')
    console.log('   <EMAIL> (User, KYC ✓)')
    console.log('   <EMAIL> (User, KYC ✗)')
    
  } catch (error) {
    console.error('💥 Seeding failed:', error.message)
    process.exit(1)
  }
}

// Run the script
main()