#!/bin/bash

# IBC-CIE Local Development Setup Script
# This script sets up the complete local development environment with Supabase

set -e

echo "🚀 Setting up IBC-CIE local development environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_dependencies() {
    print_status "Checking dependencies..."
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 18+ and try again."
        exit 1
    fi
    
    # Check pnpm
    if ! command -v pnpm &> /dev/null; then
        print_warning "pnpm is not installed. Installing pnpm..."
        npm install -g pnpm
    fi
    
    # Check Supabase CLI
    if ! command -v supabase &> /dev/null; then
        print_warning "Supabase CLI is not installed. Installing Supabase CLI..."
        brew install supabase/tap/supabase || npm install -g supabase
    fi
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker and try again."
        print_status "Visit: https://docs.docker.com/get-docker/"
        exit 1
    fi
    
    # Check if Docker is running
    if ! docker info &> /dev/null; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    
    print_success "All dependencies are available"
}

# Install npm dependencies
install_dependencies() {
    print_status "Installing project dependencies..."
    
    if [ -f "pnpm-lock.yaml" ]; then
        pnpm install
    else
        print_warning "pnpm-lock.yaml not found. Installing dependencies with npm..."
        npm install
    fi
    
    print_success "Dependencies installed"
}

# Setup environment files
setup_environment() {
    print_status "Setting up environment files..."
    
    # Copy environment files if they don't exist
    if [ ! -f ".env.local" ]; then
        if [ -f ".env.local.example" ]; then
            cp .env.local.example .env.local
            print_success "Created .env.local from example"
        else
            print_warning ".env.local.example not found. Creating basic .env.local..."
            cat > .env.local << EOF
# Supabase Local Development
SUPABASE_URL=http://127.0.0.1:54321
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
VITE_SERVER_URL=http://localhost:3000
CORS_ORIGIN=http://localhost:5174

# Development Settings
NODE_ENV=development
DATABASE_URL=postgresql://postgres:postgres@127.0.0.1:54322/postgres

# OpenAI API Key (replace with your actual key)
OPENAI_API_KEY=your-openai-key-here
EOF
            print_success "Created basic .env.local"
        fi
    else
        print_warning ".env.local already exists. Skipping..."
    fi
    
    # Set up web app environment
    if [ ! -f "apps/web/.env.local" ]; then
        cat > apps/web/.env.local << EOF
VITE_SUPABASE_URL=http://127.0.0.1:54321
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
VITE_SERVER_URL=http://localhost:3000
EOF
        print_success "Created apps/web/.env.local"
    fi
}

# Initialize Supabase
setup_supabase() {
    print_status "Setting up Supabase..."
    
    # Initialize Supabase if not already done
    if [ ! -f "supabase/config.toml" ]; then
        print_status "Initializing Supabase project..."
        supabase init
    fi
    
    # Start Supabase
    print_status "Starting Supabase services..."
    supabase start
    
    # Wait for services to be ready
    print_status "Waiting for Supabase services to be ready..."
    sleep 10
    
    # Check if database is ready
    max_attempts=30
    attempt=0
    while ! supabase status &> /dev/null && [ $attempt -lt $max_attempts ]; do
        print_status "Waiting for Supabase to be ready... (attempt $((attempt + 1))/$max_attempts)"
        sleep 2
        attempt=$((attempt + 1))
    done
    
    if [ $attempt -eq $max_attempts ]; then
        print_error "Supabase failed to start within expected time. Please check Docker and try again."
        exit 1
    fi
    
    print_success "Supabase is running"
}

# Run database migrations and seed
setup_database() {
    print_status "Setting up database..."
    
    # Apply migrations
    if [ -d "supabase/migrations" ] && [ "$(ls -A supabase/migrations)" ]; then
        print_status "Applying database migrations..."
        supabase db reset --linked=false
    else
        print_warning "No migrations found. Database will use default schema."
    fi
    
    # Generate TypeScript types
    print_status "Generating TypeScript types..."
    pnpm db:types
    
    print_success "Database setup completed"
}

# Validate setup
validate_setup() {
    print_status "Validating setup..."
    
    # Check Supabase status
    if supabase status &> /dev/null; then
        print_success "✅ Supabase is running"
    else
        print_error "❌ Supabase is not running properly"
        return 1
    fi
    
    # Check if types were generated
    if [ -f "apps/server/src/types/supabase.ts" ]; then
        print_success "✅ TypeScript types generated"
    else
        print_warning "⚠️  TypeScript types not found"
    fi
    
    # Check environment files
    if [ -f ".env.local" ]; then
        print_success "✅ Environment files configured"
    else
        print_error "❌ Environment files missing"
        return 1
    fi
    
    print_success "Setup validation completed"
}

# Display URLs and next steps
show_info() {
    echo ""
    echo "🎉 Local development environment is ready!"
    echo ""
    echo "📍 Service URLs:"
    echo "   • Supabase Studio: http://localhost:54323"
    echo "   • Database: postgresql://postgres:postgres@localhost:54322/postgres"
    echo "   • API: http://localhost:54321"
    echo "   • Inbucket (Email): http://localhost:54324"
    echo ""
    echo "🚀 Next steps:"
    echo "   1. Update your OpenAI API key in .env.local"
    echo "   2. Run 'pnpm dev' to start the development servers"
    echo "   3. Visit http://localhost:5174 (web) and http://localhost:3000 (server)"
    echo ""
    echo "📚 Useful commands:"
    echo "   • pnpm dev              - Start all development servers"
    echo "   • pnpm supabase:studio  - Open Supabase Studio"
    echo "   • pnpm supabase:reset   - Reset database with fresh data"
    echo "   • pnpm db:types         - Regenerate TypeScript types"
    echo ""
}

# Main execution
main() {
    echo "Setting up IBC-CIE local development environment..."
    echo "This will install dependencies, configure Supabase, and set up the database."
    echo ""
    
    check_dependencies
    install_dependencies
    setup_environment
    setup_supabase
    setup_database
    validate_setup
    show_info
    
    print_success "Setup completed successfully! 🎉"
}

# Run main function
main "$@"