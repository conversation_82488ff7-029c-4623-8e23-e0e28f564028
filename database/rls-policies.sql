-- =====================================================
-- IBC-CIE Row Level Security (RLS) Policies
-- Community Intelligence Engine - Blockchain Project Evaluation Platform
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE review_responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_analyses ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- HELPER FUNCTIONS
-- =====================================================

-- Function to check if user is admin (has @ibc.media email)
CREATE OR REPLACE FUNCTION is_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN (
    SELECT CASE 
      WHEN auth.jwt() ->> 'email' LIKE '%@ibc.media' THEN TRUE
      ELSE FALSE
    END
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get current user's ID from JWT
CREATE OR REPLACE FUNCTION current_user_id()
RETURNS TEXT AS $$
BEGIN
  RETURN auth.jwt() ->> 'sub';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get current user's email from JWT
CREATE OR REPLACE FUNCTION current_user_email()
RETURNS TEXT AS $$
BEGIN
  RETURN auth.jwt() ->> 'email';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if current user is KYC verified
CREATE OR REPLACE FUNCTION is_kyc_verified()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN (
    SELECT COALESCE(is_kyc_verified, FALSE)
    FROM users 
    WHERE id = current_user_id()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user exists and is authenticated
CREATE OR REPLACE FUNCTION is_authenticated()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN current_user_id() IS NOT NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- USERS TABLE POLICIES
-- =====================================================

-- Users can read their own profile data
-- Admins can read all user profiles
CREATE POLICY "users_select_policy" ON users
  FOR SELECT
  USING (
    id = current_user_id() OR is_admin()
  );

-- Users can insert their own profile during registration
-- System can create users (for auth integration)
CREATE POLICY "users_insert_policy" ON users
  FOR INSERT
  WITH CHECK (
    id = current_user_id() OR is_admin()
  );

-- Users can update their own profile data
-- Admins can update any user profile (for KYC verification)
CREATE POLICY "users_update_policy" ON users
  FOR UPDATE
  USING (
    id = current_user_id() OR is_admin()
  )
  WITH CHECK (
    id = current_user_id() OR is_admin()
  );

-- Only admins can delete users
CREATE POLICY "users_delete_policy" ON users
  FOR DELETE
  USING (is_admin());

-- =====================================================
-- PROJECTS TABLE POLICIES
-- =====================================================

-- Public can read approved projects
-- Admins can read all projects
CREATE POLICY "projects_select_policy" ON projects
  FOR SELECT
  USING (
    is_approved_for_voting = TRUE OR is_admin()
  );

-- Only admins can create new projects
CREATE POLICY "projects_insert_policy" ON projects
  FOR INSERT
  WITH CHECK (is_admin());

-- Only admins can update projects
CREATE POLICY "projects_update_policy" ON projects
  FOR UPDATE
  USING (is_admin())
  WITH CHECK (is_admin());

-- Only admins can delete projects
CREATE POLICY "projects_delete_policy" ON projects
  FOR DELETE
  USING (is_admin());

-- =====================================================
-- REVIEWS TABLE POLICIES
-- =====================================================

-- Public can read all reviews for transparency
-- This promotes community trust and open evaluation
CREATE POLICY "reviews_select_policy" ON reviews
  FOR SELECT
  USING (TRUE);

-- Only KYC-verified users can submit reviews
-- Ensures review quality and prevents spam
CREATE POLICY "reviews_insert_policy" ON reviews
  FOR INSERT
  WITH CHECK (
    is_authenticated() AND 
    is_kyc_verified() AND
    user_id = current_user_id()
  );

-- Users can only update their own reviews
-- Admins can update any review (for moderation)
CREATE POLICY "reviews_update_policy" ON reviews
  FOR UPDATE
  USING (
    user_id = current_user_id() OR is_admin()
  )
  WITH CHECK (
    user_id = current_user_id() OR is_admin()
  );

-- Users can delete their own reviews
-- Admins can delete any review (for moderation)
CREATE POLICY "reviews_delete_policy" ON reviews
  FOR DELETE
  USING (
    user_id = current_user_id() OR is_admin()
  );

-- =====================================================
-- REVIEW_RESPONSES TABLE POLICIES
-- =====================================================

-- Public can read all review responses for transparency
CREATE POLICY "review_responses_select_policy" ON review_responses
  FOR SELECT
  USING (TRUE);

-- Users can only create responses for their own reviews
-- System can create responses during review submission
CREATE POLICY "review_responses_insert_policy" ON review_responses
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM reviews 
      WHERE id = review_id AND user_id = current_user_id()
    ) OR is_admin()
  );

-- Users can only update their own review responses
-- Admins can update any response (for moderation)
CREATE POLICY "review_responses_update_policy" ON review_responses
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM reviews 
      WHERE id = review_id AND user_id = current_user_id()
    ) OR is_admin()
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM reviews 
      WHERE id = review_id AND user_id = current_user_id()
    ) OR is_admin()
  );

-- Users can delete their own review responses
-- Admins can delete any response (for moderation)
CREATE POLICY "review_responses_delete_policy" ON review_responses
  FOR DELETE
  USING (
    EXISTS (
      SELECT 1 FROM reviews 
      WHERE id = review_id AND user_id = current_user_id()
    ) OR is_admin()
  );

-- =====================================================
-- AI_ANALYSES TABLE POLICIES
-- =====================================================

-- Public can read all AI analyses for transparency
-- This allows community to see AI-generated insights
CREATE POLICY "ai_analyses_select_policy" ON ai_analyses
  FOR SELECT
  USING (TRUE);

-- Only system/admins can create AI analyses
-- Prevents users from creating fake AI analyses
CREATE POLICY "ai_analyses_insert_policy" ON ai_analyses
  FOR INSERT
  WITH CHECK (is_admin());

-- Only system/admins can update AI analyses
-- Maintains integrity of AI-generated content
CREATE POLICY "ai_analyses_update_policy" ON ai_analyses
  FOR UPDATE
  USING (is_admin())
  WITH CHECK (is_admin());

-- Only admins can delete AI analyses
CREATE POLICY "ai_analyses_delete_policy" ON ai_analyses
  FOR DELETE
  USING (is_admin());

-- =====================================================
-- ADDITIONAL SECURITY CONSTRAINTS
-- =====================================================

-- Function to validate review uniqueness (one review per user per project)
CREATE OR REPLACE FUNCTION validate_review_uniqueness()
RETURNS TRIGGER AS $$
BEGIN
  -- Check if user already has a review for this project
  IF EXISTS (
    SELECT 1 FROM reviews 
    WHERE user_id = NEW.user_id 
    AND project_id = NEW.project_id 
    AND id != COALESCE(NEW.id, '')
  ) THEN
    RAISE EXCEPTION 'User can only submit one review per project';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to enforce review uniqueness
DROP TRIGGER IF EXISTS review_uniqueness_trigger ON reviews;
CREATE TRIGGER review_uniqueness_trigger
  BEFORE INSERT OR UPDATE ON reviews
  FOR EACH ROW
  EXECUTE FUNCTION validate_review_uniqueness();

-- Function to validate KYC requirement for reviews
CREATE OR REPLACE FUNCTION validate_kyc_for_reviews()
RETURNS TRIGGER AS $$
BEGIN
  -- Check if user is KYC verified
  IF NOT EXISTS (
    SELECT 1 FROM users 
    WHERE id = NEW.user_id 
    AND is_kyc_verified = TRUE
  ) THEN
    RAISE EXCEPTION 'User must be KYC verified to submit reviews';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to enforce KYC requirement for reviews
DROP TRIGGER IF EXISTS kyc_validation_trigger ON reviews;
CREATE TRIGGER kyc_validation_trigger
  BEFORE INSERT ON reviews
  FOR EACH ROW
  EXECUTE FUNCTION validate_kyc_for_reviews();

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Indexes to optimize RLS policy performance
CREATE INDEX IF NOT EXISTS idx_users_email_domain ON users ((email LIKE '%@ibc.media'));
CREATE INDEX IF NOT EXISTS idx_users_kyc_verified ON users (is_kyc_verified);
CREATE INDEX IF NOT EXISTS idx_reviews_user_project ON reviews (user_id, project_id);
CREATE INDEX IF NOT EXISTS idx_projects_approved ON projects (is_approved_for_voting);
CREATE INDEX IF NOT EXISTS idx_review_responses_review_id ON review_responses (review_id);

-- =====================================================
-- GRANT PERMISSIONS
-- =====================================================

-- Grant execute permissions on helper functions to authenticated users
GRANT EXECUTE ON FUNCTION is_admin() TO authenticated;
GRANT EXECUTE ON FUNCTION current_user_id() TO authenticated;
GRANT EXECUTE ON FUNCTION current_user_email() TO authenticated;
GRANT EXECUTE ON FUNCTION is_kyc_verified() TO authenticated;
GRANT EXECUTE ON FUNCTION is_authenticated() TO authenticated;

-- Grant permissions for anon users to read public data
GRANT SELECT ON projects TO anon;
GRANT SELECT ON reviews TO anon;
GRANT SELECT ON review_responses TO anon;
GRANT SELECT ON ai_analyses TO anon;

-- =====================================================
-- TESTING POLICIES
-- =====================================================

-- Test helper function to simulate different user contexts
CREATE OR REPLACE FUNCTION test_as_user(user_email TEXT, user_id TEXT DEFAULT NULL)
RETURNS TEXT AS $$
DECLARE
  result TEXT;
BEGIN
  -- This is a helper function for testing RLS policies
  -- In production, JWT tokens would be handled by Supabase Auth
  result := format('Testing as user: %s (ID: %s)', user_email, COALESCE(user_id, 'unknown'));
  
  -- Example usage in tests:
  -- SELECT test_as_user('<EMAIL>', 'user_123');
  -- SELECT test_as_user('<EMAIL>', 'user_456');
  
  RETURN result;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- POLICY VALIDATION QUERIES
-- =====================================================

-- Test queries to validate RLS policies are working correctly
-- Run these after implementing policies to ensure proper security

-- 1. Test user access to their own data
-- SELECT * FROM users WHERE id = current_user_id();

-- 2. Test admin access to all users
-- SELECT COUNT(*) FROM users; -- Should return all users for admin, only own for regular user

-- 3. Test public access to approved projects
-- SELECT COUNT(*) FROM projects WHERE is_approved_for_voting = TRUE;

-- 4. Test KYC requirement for reviews
-- INSERT INTO reviews (user_id, project_id, overall_sentiment) VALUES (current_user_id(), 'project_123', TRUE);

-- 5. Test review uniqueness constraint
-- INSERT INTO reviews (user_id, project_id, overall_sentiment) VALUES (current_user_id(), 'project_123', FALSE);

-- =====================================================
-- MONITORING AND AUDITING
-- =====================================================

-- Function to log security events
CREATE OR REPLACE FUNCTION log_security_event(
  event_type TEXT,
  table_name TEXT,
  user_id TEXT DEFAULT NULL,
  details JSONB DEFAULT '{}'::jsonb
)
RETURNS VOID AS $$
BEGIN
  -- Log security events for auditing
  -- In production, this could write to a separate audit table
  RAISE NOTICE 'Security Event - Type: %, Table: %, User: %, Details: %', 
    event_type, table_name, COALESCE(user_id, current_user_id()), details;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- POLICY DOCUMENTATION
-- =====================================================

COMMENT ON FUNCTION is_admin() IS 'Checks if current user has admin privileges (@ibc.media email domain)';
COMMENT ON FUNCTION current_user_id() IS 'Returns the current authenticated user ID from JWT token';
COMMENT ON FUNCTION is_kyc_verified() IS 'Checks if current user has completed KYC verification';

COMMENT ON POLICY "users_select_policy" ON users IS 'Users can read own profile, admins read all';
COMMENT ON POLICY "projects_select_policy" ON projects IS 'Public reads approved projects, admins read all';
COMMENT ON POLICY "reviews_select_policy" ON reviews IS 'Public transparency - all reviews readable';
COMMENT ON POLICY "review_responses_select_policy" ON review_responses IS 'Public transparency - all responses readable';
COMMENT ON POLICY "ai_analyses_select_policy" ON ai_analyses IS 'Public transparency - all AI analyses readable';

-- =====================================================
-- END OF RLS POLICIES
-- =====================================================