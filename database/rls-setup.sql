-- =====================================================
-- IBC-CIE RLS SETUP AND MIGRATION SCRIPT
-- Complete setup for Row Level Security implementation
-- =====================================================

-- =====================================================
-- PRE-DEPLOYMENT CHECKLIST
-- =====================================================

/*
BEFORE RUNNING THIS SCRIPT:

1. BACKUP YOUR DATABASE:
   pg_dump your_database > backup_before_rls.sql

2. VERIFY ENVIRONMENT:
   - Ensure you're running on the correct database
   - Verify you have SUPERUSER privileges
   - Check that Supabase Auth is properly configured

3. REVIEW AUTHENTICATION SETUP:
   - Ensure JWT tokens contain required claims (email, sub)
   - Verify auth.jwt() function is available
   - Test authentication flow end-to-end

4. PLAN ROLLBACK STRATEGY:
   - Prepare rollback script if needed
   - Test in staging environment first
   - Schedule deployment during low-traffic period
*/

-- =====================================================
-- ENVIRONMENT VERIFICATION
-- =====================================================

-- Check if we're on Supabase (has auth schema)
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.schemata WHERE schema_name = 'auth') THEN
    RAISE EXCEPTION 'This script requires Supabase auth schema. Are you running on Supabase?';
  END IF;
END $$;

-- Check current user privileges
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_roles 
    WHERE rolname = current_user 
    AND (rolsuper = true OR rolcreatedb = true)
  ) THEN
    RAISE WARNING 'Current user may not have sufficient privileges for RLS setup';
  END IF;
END $$;

-- Verify required tables exist
DO $$
DECLARE
  missing_tables TEXT[] := ARRAY[]::TEXT[];
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'users') THEN
    missing_tables := array_append(missing_tables, 'users');
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'projects') THEN
    missing_tables := array_append(missing_tables, 'projects');
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'reviews') THEN
    missing_tables := array_append(missing_tables, 'reviews');
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'review_responses') THEN
    missing_tables := array_append(missing_tables, 'review_responses');
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'ai_analyses') THEN
    missing_tables := array_append(missing_tables, 'ai_analyses');
  END IF;
  
  IF array_length(missing_tables, 1) > 0 THEN
    RAISE EXCEPTION 'Missing required tables: %', array_to_string(missing_tables, ', ');
  END IF;
END $$;

-- =====================================================
-- BACKUP EXISTING POLICIES (if any)
-- =====================================================

-- Create backup table for existing policies
CREATE TABLE IF NOT EXISTS rls_policy_backup (
  id SERIAL PRIMARY KEY,
  backup_date TIMESTAMP DEFAULT NOW(),
  table_name TEXT,
  policy_name TEXT,
  policy_definition TEXT
);

-- Backup existing RLS policies
INSERT INTO rls_policy_backup (table_name, policy_name, policy_definition)
SELECT 
  tablename,
  policyname,
  format('CREATE POLICY %I ON %I FOR %s TO %s USING (%s)%s;',
    policyname,
    tablename,
    cmd,
    array_to_string(roles, ', '),
    COALESCE(qual, 'TRUE'),
    CASE WHEN with_check IS NOT NULL THEN format(' WITH CHECK (%s)', with_check) ELSE '' END
  )
FROM pg_policies 
WHERE schemaname = 'public'
  AND tablename IN ('users', 'projects', 'reviews', 'review_responses', 'ai_analyses');

-- =====================================================
-- DISABLE EXISTING RLS (if enabled)
-- =====================================================

-- Drop existing policies if they exist
DO $$
DECLARE
  pol RECORD;
BEGIN
  FOR pol IN 
    SELECT tablename, policyname 
    FROM pg_policies 
    WHERE schemaname = 'public'
      AND tablename IN ('users', 'projects', 'reviews', 'review_responses', 'ai_analyses')
  LOOP
    EXECUTE format('DROP POLICY IF EXISTS %I ON %I', pol.policyname, pol.tablename);
  END LOOP;
END $$;

-- Disable RLS temporarily for setup
ALTER TABLE IF EXISTS users DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS projects DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS reviews DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS review_responses DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS ai_analyses DISABLE ROW LEVEL SECURITY;

-- =====================================================
-- CREATE SECURITY SCHEMA
-- =====================================================

-- Create dedicated schema for security functions
CREATE SCHEMA IF NOT EXISTS security;

-- Grant usage on security schema
GRANT USAGE ON SCHEMA security TO authenticated, anon;

-- =====================================================
-- CORE SECURITY FUNCTIONS
-- =====================================================

-- Move security functions to dedicated schema
CREATE OR REPLACE FUNCTION security.current_user_id()
RETURNS TEXT 
LANGUAGE SQL 
SECURITY DEFINER
STABLE
AS $$
  SELECT COALESCE(
    auth.jwt() ->> 'sub',
    current_setting('request.jwt.claims', true)::json ->> 'sub'
  );
$$;

CREATE OR REPLACE FUNCTION security.current_user_email()
RETURNS TEXT 
LANGUAGE SQL 
SECURITY DEFINER
STABLE
AS $$
  SELECT COALESCE(
    auth.jwt() ->> 'email',
    current_setting('request.jwt.claims', true)::json ->> 'email'
  );
$$;

CREATE OR REPLACE FUNCTION security.is_admin()
RETURNS BOOLEAN 
LANGUAGE SQL 
SECURITY DEFINER
STABLE
AS $$
  SELECT security.current_user_email() LIKE '%@ibc.media';
$$;

CREATE OR REPLACE FUNCTION security.is_authenticated()
RETURNS BOOLEAN 
LANGUAGE SQL 
SECURITY DEFINER
STABLE
AS $$
  SELECT security.current_user_id() IS NOT NULL;
$$;

CREATE OR REPLACE FUNCTION security.is_kyc_verified()
RETURNS BOOLEAN 
LANGUAGE SQL 
SECURITY DEFINER
STABLE
AS $$
  SELECT COALESCE(
    (SELECT is_kyc_verified FROM users WHERE id = security.current_user_id()),
    FALSE
  );
$$;

-- Enhanced function with caching for better performance
CREATE OR REPLACE FUNCTION security.user_can_access_project(project_id TEXT)
RETURNS BOOLEAN 
LANGUAGE SQL 
SECURITY DEFINER
STABLE
AS $$
  SELECT 
    EXISTS (
      SELECT 1 FROM projects 
      WHERE id = user_can_access_project.project_id 
      AND (is_approved_for_voting = TRUE OR security.is_admin())
    );
$$;

-- =====================================================
-- GRANT PERMISSIONS ON SECURITY FUNCTIONS
-- =====================================================

GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA security TO authenticated, anon;

-- =====================================================
-- OPTIMIZED RLS POLICIES
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE review_responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_analyses ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- USERS TABLE POLICIES
-- =====================================================

CREATE POLICY users_select_own_or_admin ON users
  FOR SELECT
  USING (
    id = security.current_user_id() 
    OR security.is_admin()
  );

CREATE POLICY users_insert_own_or_admin ON users
  FOR INSERT
  WITH CHECK (
    id = security.current_user_id() 
    OR security.is_admin()
  );

CREATE POLICY users_update_own_or_admin ON users
  FOR UPDATE
  USING (
    id = security.current_user_id() 
    OR security.is_admin()
  )
  WITH CHECK (
    id = security.current_user_id() 
    OR security.is_admin()
  );

CREATE POLICY users_delete_admin_only ON users
  FOR DELETE
  USING (security.is_admin());

-- =====================================================
-- PROJECTS TABLE POLICIES
-- =====================================================

CREATE POLICY projects_select_approved_or_admin ON projects
  FOR SELECT
  USING (
    is_approved_for_voting = TRUE 
    OR security.is_admin()
  );

CREATE POLICY projects_insert_admin_only ON projects
  FOR INSERT
  WITH CHECK (security.is_admin());

CREATE POLICY projects_update_admin_only ON projects
  FOR UPDATE
  USING (security.is_admin())
  WITH CHECK (security.is_admin());

CREATE POLICY projects_delete_admin_only ON projects
  FOR DELETE
  USING (security.is_admin());

-- =====================================================
-- REVIEWS TABLE POLICIES
-- =====================================================

CREATE POLICY reviews_select_all ON reviews
  FOR SELECT
  USING (TRUE);

CREATE POLICY reviews_insert_kyc_verified ON reviews
  FOR INSERT
  WITH CHECK (
    security.is_authenticated()
    AND security.is_kyc_verified()
    AND user_id = security.current_user_id()
  );

CREATE POLICY reviews_update_own_or_admin ON reviews
  FOR UPDATE
  USING (
    user_id = security.current_user_id() 
    OR security.is_admin()
  )
  WITH CHECK (
    user_id = security.current_user_id() 
    OR security.is_admin()
  );

CREATE POLICY reviews_delete_own_or_admin ON reviews
  FOR DELETE
  USING (
    user_id = security.current_user_id() 
    OR security.is_admin()
  );

-- =====================================================
-- REVIEW_RESPONSES TABLE POLICIES
-- =====================================================

CREATE POLICY review_responses_select_all ON review_responses
  FOR SELECT
  USING (TRUE);

CREATE POLICY review_responses_insert_own_review ON review_responses
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM reviews 
      WHERE id = review_id 
      AND user_id = security.current_user_id()
    ) 
    OR security.is_admin()
  );

CREATE POLICY review_responses_update_own_review ON review_responses
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM reviews 
      WHERE id = review_id 
      AND user_id = security.current_user_id()
    ) 
    OR security.is_admin()
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM reviews 
      WHERE id = review_id 
      AND user_id = security.current_user_id()
    ) 
    OR security.is_admin()
  );

CREATE POLICY review_responses_delete_own_review ON review_responses
  FOR DELETE
  USING (
    EXISTS (
      SELECT 1 FROM reviews 
      WHERE id = review_id 
      AND user_id = security.current_user_id()
    ) 
    OR security.is_admin()
  );

-- =====================================================
-- AI_ANALYSES TABLE POLICIES
-- =====================================================

CREATE POLICY ai_analyses_select_all ON ai_analyses
  FOR SELECT
  USING (TRUE);

CREATE POLICY ai_analyses_insert_admin_only ON ai_analyses
  FOR INSERT
  WITH CHECK (security.is_admin());

CREATE POLICY ai_analyses_update_admin_only ON ai_analyses
  FOR UPDATE
  USING (security.is_admin())
  WITH CHECK (security.is_admin());

CREATE POLICY ai_analyses_delete_admin_only ON ai_analyses
  FOR DELETE
  USING (security.is_admin());

-- =====================================================
-- PERFORMANCE OPTIMIZATIONS
-- =====================================================

-- Create optimized indexes for RLS performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email_domain 
ON users ((email LIKE '%@ibc.media'));

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_kyc_verified 
ON users (is_kyc_verified) 
WHERE is_kyc_verified = TRUE;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_projects_approved 
ON projects (is_approved_for_voting) 
WHERE is_approved_for_voting = TRUE;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_reviews_user_project 
ON reviews (user_id, project_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_review_responses_review_id 
ON review_responses (review_id);

-- =====================================================
-- VALIDATION TRIGGERS
-- =====================================================

-- Enhanced review uniqueness validation
CREATE OR REPLACE FUNCTION security.validate_review_constraints()
RETURNS TRIGGER 
LANGUAGE plpgsql 
SECURITY DEFINER
AS $$
BEGIN
  -- Check review uniqueness
  IF EXISTS (
    SELECT 1 FROM reviews 
    WHERE user_id = NEW.user_id 
    AND project_id = NEW.project_id 
    AND id != COALESCE(NEW.id, '')
  ) THEN
    RAISE EXCEPTION 'User can only submit one review per project';
  END IF;
  
  -- Check KYC verification
  IF NOT EXISTS (
    SELECT 1 FROM users 
    WHERE id = NEW.user_id 
    AND is_kyc_verified = TRUE
  ) THEN
    RAISE EXCEPTION 'User must be KYC verified to submit reviews';
  END IF;
  
  -- Check project exists and is approved for voting
  IF NOT EXISTS (
    SELECT 1 FROM projects 
    WHERE id = NEW.project_id 
    AND is_approved_for_voting = TRUE
  ) THEN
    RAISE EXCEPTION 'Cannot review unapproved or non-existent project';
  END IF;
  
  RETURN NEW;
END;
$$;

-- Apply validation trigger
DROP TRIGGER IF EXISTS review_validation_trigger ON reviews;
CREATE TRIGGER review_validation_trigger
  BEFORE INSERT OR UPDATE ON reviews
  FOR EACH ROW
  EXECUTE FUNCTION security.validate_review_constraints();

-- =====================================================
-- MONITORING AND LOGGING
-- =====================================================

-- Create audit log table
CREATE TABLE IF NOT EXISTS security.audit_log (
  id BIGSERIAL PRIMARY KEY,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  user_id TEXT,
  action TEXT,
  table_name TEXT,
  record_id TEXT,
  old_values JSONB,
  new_values JSONB,
  ip_address INET,
  user_agent TEXT
);

-- Enable RLS on audit log
ALTER TABLE security.audit_log ENABLE ROW LEVEL SECURITY;

-- Only admins can read audit logs
CREATE POLICY audit_log_admin_only ON security.audit_log
  FOR ALL
  USING (security.is_admin())
  WITH CHECK (security.is_admin());

-- Audit trigger function
CREATE OR REPLACE FUNCTION security.audit_trigger()
RETURNS TRIGGER 
LANGUAGE plpgsql 
SECURITY DEFINER
AS $$
BEGIN
  INSERT INTO security.audit_log (
    user_id,
    action,
    table_name,
    record_id,
    old_values,
    new_values
  ) VALUES (
    security.current_user_id(),
    TG_OP,
    TG_TABLE_NAME,
    COALESCE(NEW.id, OLD.id),
    CASE WHEN TG_OP = 'DELETE' THEN to_jsonb(OLD) ELSE NULL END,
    CASE WHEN TG_OP IN ('INSERT', 'UPDATE') THEN to_jsonb(NEW) ELSE NULL END
  );
  
  RETURN COALESCE(NEW, OLD);
END;
$$;

-- Apply audit triggers to sensitive tables
CREATE TRIGGER users_audit_trigger
  AFTER INSERT OR UPDATE OR DELETE ON users
  FOR EACH ROW EXECUTE FUNCTION security.audit_trigger();

CREATE TRIGGER projects_audit_trigger
  AFTER INSERT OR UPDATE OR DELETE ON projects
  FOR EACH ROW EXECUTE FUNCTION security.audit_trigger();

-- =====================================================
-- GRANT NECESSARY PERMISSIONS
-- =====================================================

-- Grant permissions for anon users (public access)
GRANT SELECT ON projects TO anon;
GRANT SELECT ON reviews TO anon;
GRANT SELECT ON review_responses TO anon;
GRANT SELECT ON ai_analyses TO anon;

-- Grant permissions for authenticated users
GRANT ALL ON users TO authenticated;
GRANT SELECT ON projects TO authenticated;
GRANT ALL ON reviews TO authenticated;
GRANT ALL ON review_responses TO authenticated;
GRANT SELECT ON ai_analyses TO authenticated;

-- =====================================================
-- VALIDATION AND HEALTH CHECK
-- =====================================================

-- Function to validate RLS setup
CREATE OR REPLACE FUNCTION security.validate_rls_setup()
RETURNS TABLE(
  table_name TEXT,
  rls_enabled BOOLEAN,
  policy_count INTEGER,
  status TEXT
) 
LANGUAGE plpgsql 
SECURITY DEFINER
AS $$
DECLARE
  expected_tables TEXT[] := ARRAY['users', 'projects', 'reviews', 'review_responses', 'ai_analyses'];
  tbl TEXT;
  rls_status BOOLEAN;
  pol_count INTEGER;
BEGIN
  FOREACH tbl IN ARRAY expected_tables LOOP
    -- Check if RLS is enabled
    SELECT c.relrowsecurity INTO rls_status
    FROM pg_class c
    JOIN pg_namespace n ON c.relnamespace = n.oid
    WHERE n.nspname = 'public' AND c.relname = tbl;
    
    -- Count policies
    SELECT COUNT(*) INTO pol_count
    FROM pg_policies
    WHERE schemaname = 'public' AND tablename = tbl;
    
    RETURN QUERY SELECT 
      tbl,
      COALESCE(rls_status, FALSE),
      COALESCE(pol_count, 0),
      CASE 
        WHEN NOT COALESCE(rls_status, FALSE) THEN 'ERROR: RLS not enabled'
        WHEN COALESCE(pol_count, 0) = 0 THEN 'WARNING: No policies defined'
        WHEN COALESCE(pol_count, 0) < 4 THEN 'WARNING: Few policies'
        ELSE 'OK'
      END;
  END LOOP;
END;
$$;

-- =====================================================
-- POST-DEPLOYMENT VERIFICATION
-- =====================================================

-- Run validation
SELECT * FROM security.validate_rls_setup();

-- Check function availability
SELECT 
  proname as function_name,
  prosecdef as security_definer
FROM pg_proc 
WHERE pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'security')
ORDER BY proname;

-- Verify indexes were created
SELECT 
  indexname,
  tablename,
  indexdef
FROM pg_indexes
WHERE schemaname = 'public'
  AND indexname LIKE 'idx_%'
  AND tablename IN ('users', 'projects', 'reviews', 'review_responses', 'ai_analyses')
ORDER BY tablename, indexname;

-- =====================================================
-- ROLLBACK SCRIPT GENERATION
-- =====================================================

-- Generate rollback commands
CREATE OR REPLACE FUNCTION security.generate_rollback_script()
RETURNS TEXT 
LANGUAGE plpgsql 
SECURITY DEFINER
AS $$
DECLARE
  rollback_script TEXT := '';
BEGIN
  rollback_script := rollback_script || '-- ROLLBACK SCRIPT FOR RLS SETUP' || E'\n';
  rollback_script := rollback_script || '-- Generated: ' || NOW() || E'\n\n';
  
  -- Disable RLS
  rollback_script := rollback_script || 'ALTER TABLE users DISABLE ROW LEVEL SECURITY;' || E'\n';
  rollback_script := rollback_script || 'ALTER TABLE projects DISABLE ROW LEVEL SECURITY;' || E'\n';
  rollback_script := rollback_script || 'ALTER TABLE reviews DISABLE ROW LEVEL SECURITY;' || E'\n';
  rollback_script := rollback_script || 'ALTER TABLE review_responses DISABLE ROW LEVEL SECURITY;' || E'\n';
  rollback_script := rollback_script || 'ALTER TABLE ai_analyses DISABLE ROW LEVEL SECURITY;' || E'\n\n';
  
  -- Drop security schema
  rollback_script := rollback_script || 'DROP SCHEMA IF EXISTS security CASCADE;' || E'\n\n';
  
  -- Restore original policies from backup
  rollback_script := rollback_script || '-- Restore original policies from rls_policy_backup table' || E'\n';
  rollback_script := rollback_script || '-- (Manual step - review backup table and restore as needed)' || E'\n';
  
  RETURN rollback_script;
END;
$$;

-- =====================================================
-- SUCCESS MESSAGE
-- =====================================================

DO $$
BEGIN
  RAISE NOTICE 'RLS setup completed successfully!';
  RAISE NOTICE 'Next steps:';
  RAISE NOTICE '1. Run comprehensive tests using rls-tests.sql';
  RAISE NOTICE '2. Verify authentication integration';
  RAISE NOTICE '3. Test with different user roles';
  RAISE NOTICE '4. Monitor performance with real data';
  RAISE NOTICE '5. Set up monitoring alerts';
END $$;

-- =====================================================
-- END OF SETUP SCRIPT
-- =====================================================