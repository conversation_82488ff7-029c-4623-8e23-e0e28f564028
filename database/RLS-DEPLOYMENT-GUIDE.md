# IBC-CIE Row Level Security (RLS) Deployment Guide

## 🛡️ Overview

This guide provides comprehensive Row Level Security policies for the IBC-CIE blockchain project evaluation platform. The RLS implementation ensures data security, user privacy, and proper access control across all database operations.

## 📋 Pre-Deployment Checklist

### 1. Environment Verification
- [ ] **Database Backup**: Create full backup before deployment
- [ ] **Supabase Setup**: Ensure Supabase Auth is properly configured  
- [ ] **JWT Configuration**: Verify JWT tokens contain required claims (`sub`, `email`)
- [ ] **Admin Users**: Confirm admin users have `@ibc.media` email addresses
- [ ] **Staging Test**: Test all policies in staging environment first

### 2. Required Permissions
- [ ] **Database Superuser**: Required for creating security functions
- [ ] **Schema Permissions**: Access to `public` and `auth` schemas
- [ ] **Function Creation**: Ability to create SECURITY DEFINER functions

## 🚀 Deployment Steps

### Step 1: Backup and Preparation
```sql
-- Create database backup
pg_dump your_database > backup_before_rls_$(date +%Y%m%d).sql

-- Verify environment
SELECT current_user, current_database();
```

### Step 2: Execute Setup Script
```bash
# Run the main setup script
psql -d your_database -f database/rls-setup.sql
```

### Step 3: Validation
```sql
-- Verify RLS is enabled
SELECT * FROM security.validate_rls_setup();

-- Check security functions
SELECT proname FROM pg_proc WHERE pronamespace = (
  SELECT oid FROM pg_namespace WHERE nspname = 'security'
);
```

### Step 4: Testing
```bash
# Run comprehensive test suite
psql -d your_database -f database/rls-tests.sql
```

## 🔐 Security Architecture

### Admin Role Detection
- **Email Domain**: Users with `@ibc.media` emails are automatically admins
- **Privilege Escalation**: Admins can access all data and perform administrative operations
- **Function**: `security.is_admin()` checks current user's admin status

### KYC Verification
- **Requirement**: Only KYC-verified users can submit reviews
- **Database Field**: `users.is_kyc_verified` boolean flag
- **Function**: `security.is_kyc_verified()` validates current user's KYC status

### User Context Management
- **JWT Integration**: Seamless integration with Supabase Auth
- **User ID Extraction**: `security.current_user_id()` from JWT `sub` claim
- **Email Extraction**: `security.current_user_email()` from JWT `email` claim

## 📊 Table-Specific Policies

### Users Table
| Operation | Permission | Notes |
|-----------|------------|--------|
| SELECT | Own profile + Admin | Users see only their data |
| INSERT | Own profile + Admin | Registration and admin creation |
| UPDATE | Own profile + Admin | Profile updates and KYC verification |
| DELETE | Admin only | User account deletion |

### Projects Table
| Operation | Permission | Notes |
|-----------|------------|--------|
| SELECT | Approved + Admin | Public sees approved projects only |
| INSERT | Admin only | Project creation restricted |
| UPDATE | Admin only | Project modification restricted |
| DELETE | Admin only | Project removal restricted |

### Reviews Table
| Operation | Permission | Notes |
|-----------|------------|--------|
| SELECT | Public | Full transparency for community trust |
| INSERT | KYC + Own | Only verified users can review |
| UPDATE | Own + Admin | Edit own reviews + moderation |
| DELETE | Own + Admin | Remove own reviews + moderation |

### Review Responses Table
| Operation | Permission | Notes |
|-----------|------------|--------|
| SELECT | Public | Transparency in detailed feedback |
| INSERT | Own Review | Responses tied to parent review |
| UPDATE | Own Review + Admin | Edit responses + moderation |
| DELETE | Own Review + Admin | Remove responses + moderation |

### AI Analyses Table
| Operation | Permission | Notes |
|-----------|------------|--------|
| SELECT | Public | Open access to AI insights |
| INSERT | Admin/System only | Prevent fake AI analyses |
| UPDATE | Admin/System only | Maintain analysis integrity |
| DELETE | Admin only | Analysis removal |

## 🔍 Key Security Features

### 1. Review Uniqueness Constraint
- **Enforcement**: One review per user per project
- **Implementation**: Database trigger with composite unique constraint
- **Error Handling**: Descriptive error messages for violations

### 2. KYC Verification Gate
- **Requirement**: KYC verification required for review submission
- **Implementation**: Trigger validates user KYC status before insert
- **Flexibility**: Admin override capability for special cases

### 3. Project Approval Workflow
- **Public Access**: Only approved projects visible to public
- **Admin Access**: Admins can see all projects (including unapproved)
- **Workflow**: Projects must be explicitly approved for voting

### 4. Audit Trail
- **Logging**: All sensitive operations logged to `security.audit_log`
- **Details**: User ID, action, timestamp, old/new values
- **Access**: Admin-only access to audit logs

## ⚡ Performance Optimizations

### Specialized Indexes
```sql
-- Email domain checking for admin detection
CREATE INDEX idx_users_email_domain ON users ((email LIKE '%@ibc.media'));

-- KYC verification lookup
CREATE INDEX idx_users_kyc_verified ON users (is_kyc_verified) WHERE is_kyc_verified = TRUE;

-- Project approval filtering
CREATE INDEX idx_projects_approved ON projects (is_approved_for_voting) WHERE is_approved_for_voting = TRUE;

-- Review ownership lookups
CREATE INDEX idx_reviews_user_project ON reviews (user_id, project_id);
```

### Function Optimization
- **STABLE Functions**: Security functions marked as STABLE for query planning
- **SECURITY DEFINER**: Functions run with elevated privileges
- **Minimal Computation**: Efficient logic to reduce policy evaluation time

## 🧪 Testing Strategy

### 1. Unit Tests
- Individual policy validation
- Security function testing
- Constraint enforcement verification

### 2. Integration Tests
- End-to-end user workflows
- Cross-table relationship validation
- Authentication integration testing

### 3. Security Tests
- Privilege escalation attempts
- Data access boundary testing
- SQL injection prevention

### 4. Performance Tests
- Policy execution time measurement
- Index utilization analysis
- Query plan optimization

## 📈 Monitoring and Alerts

### Key Metrics to Monitor
1. **Policy Evaluation Time**: Monitor RLS policy execution performance
2. **Failed Access Attempts**: Track unauthorized access attempts
3. **Admin Operations**: Monitor all administrative actions
4. **KYC Verification Rate**: Track user verification completion

### Recommended Alerts
- Multiple failed access attempts from same user
- Unusual admin activity patterns
- Performance degradation in policy evaluation
- High rate of constraint violations

## 🚨 Troubleshooting

### Common Issues

#### 1. JWT Token Issues
**Problem**: Security functions return NULL
**Solution**: 
- Verify Supabase Auth configuration
- Check JWT token structure
- Ensure proper claim extraction

#### 2. Policy Performance Issues
**Problem**: Slow query execution with RLS
**Solution**:
- Analyze query execution plans
- Optimize indexes for policy conditions
- Consider policy redesign for complex cases

#### 3. Admin Access Problems
**Problem**: Admin users cannot access data
**Solution**:
- Verify email domain configuration
- Check `is_admin()` function logic
- Confirm admin user email format

#### 4. KYC Verification Failures
**Problem**: Verified users cannot submit reviews
**Solution**:
- Check `is_kyc_verified` database values
- Verify trigger logic
- Test `security.is_kyc_verified()` function

## 🔄 Rollback Procedures

### Emergency Rollback
```sql
-- Disable RLS immediately
ALTER TABLE users DISABLE ROW LEVEL SECURITY;
ALTER TABLE projects DISABLE ROW LEVEL SECURITY;
ALTER TABLE reviews DISABLE ROW LEVEL SECURITY;
ALTER TABLE review_responses DISABLE ROW LEVEL SECURITY;
ALTER TABLE ai_analyses DISABLE ROW LEVEL SECURITY;

-- Drop security schema
DROP SCHEMA security CASCADE;
```

### Planned Rollback
```sql
-- Generate rollback script
SELECT security.generate_rollback_script();

-- Restore from backup
-- psql -d your_database < backup_before_rls_YYYYMMDD.sql
```

## 📚 Best Practices

### 1. Security
- **Principle of Least Privilege**: Grant minimum necessary permissions
- **Regular Audits**: Review access patterns and policy effectiveness
- **Monitoring**: Implement comprehensive logging and alerting
- **Testing**: Continuous security testing in all environments

### 2. Performance
- **Index Strategy**: Create targeted indexes for policy conditions
- **Query Optimization**: Monitor and optimize slow queries
- **Function Efficiency**: Keep security functions lightweight
- **Caching**: Leverage PostgreSQL's query planning cache

### 3. Maintenance
- **Documentation**: Keep policies well-documented
- **Version Control**: Track all policy changes
- **Testing**: Automated testing for policy modifications
- **Monitoring**: Regular performance and security reviews

## 🔗 Related Files

- `rls-setup.sql`: Main deployment script
- `rls-policies.sql`: Core RLS policies
- `rls-tests.sql`: Comprehensive test suite
- `rollback-procedures.sql`: Emergency rollback scripts

## 📞 Support

For issues or questions regarding RLS implementation:
1. Check troubleshooting section above
2. Review test results for specific error patterns
3. Consult Supabase documentation for auth-related issues
4. Monitor audit logs for security events

---

**⚠️ Important**: Always test RLS policies thoroughly in a staging environment before deploying to production. Database security is critical for user trust and regulatory compliance.