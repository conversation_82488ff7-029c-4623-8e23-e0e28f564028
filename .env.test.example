# Test Environment Configuration
# For running tests with Supabase

# Test Supabase Instance
SUPABASE_URL=http://127.0.0.1:54321
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU

# Mock OpenAI for testing
OPENAI_API_KEY=test-key-mock

# Test Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
VITE_SERVER_URL=http://localhost:3000
CORS_ORIGIN=http://localhost:5174

# Test Environment Settings
NODE_ENV=test
AUTH_SECRET=test-auth-secret-not-for-production

# Test Database
DATABASE_URL=postgresql://postgres:postgres@127.0.0.1:54322/postgres

# Test-specific Configuration
TEST_TIMEOUT=30000
SKIP_ENV_VALIDATION=true